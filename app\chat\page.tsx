"use client"

import { useState, useEffect, useRef } from "react"
import { useAuth } from "@/components/auth-provider"
import { <PERSON><PERSON><PERSON>eader } from "@/components/chat-header"
import { ChatSidebar } from "@/components/chat-sidebar"
import { ChatInterface } from "@/components/chat-interface"
import { DocumentUpload } from "@/components/document-upload"
import { useToast } from "@/components/ui/use-toast"
import { v4 as uuidv4 } from "uuid"

interface Conversation {
  id: string
  title: string
  lastMessage: string
  timestamp: number
  messages: Message[]
}

interface Message {
  id: string
  role: "user" | "assistant" | "system"
  content: string
  createdAt: Date
}

interface UploadedFile {
  id: string
  name: string
  type: string
  size: number
  content: string
  url?: string
}

export default function ChatPage() {
  const { user } = useAuth()
  const { toast } = useToast()
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([])
  const [showFileUpload, setShowFileUpload] = useState(false)
  const [selectedModel, setSelectedModel] = useState<"gemini" | "grok">("gemini")
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Load conversations from localStorage on mount
  useEffect(() => {
    if (user) {
      try {
        const savedConversations = localStorage.getItem(`chatnova_conversations_${user.id}`)
        if (savedConversations) {
          const parsedConversations = JSON.parse(savedConversations) as Conversation[]
          // Convert string dates back to Date objects
          parsedConversations.forEach(conv => {
            conv.messages.forEach(msg => {
              msg.createdAt = new Date(msg.createdAt)
            })
          })
          setConversations(parsedConversations)
          
          // If there are conversations, set the most recent one as current
          if (parsedConversations.length > 0) {
            const mostRecent = parsedConversations.sort((a, b) => b.timestamp - a.timestamp)[0]
            setCurrentConversationId(mostRecent.id)
            setMessages(mostRecent.messages)
          }
        }
      } catch (error) {
        console.error("Error loading conversations:", error)
      }
    }
  }, [user])

  // Save conversations to localStorage whenever they change
  useEffect(() => {
    if (user && conversations.length > 0) {
      localStorage.setItem(`chatnova_conversations_${user.id}`, JSON.stringify(conversations))
    }
  }, [conversations, user])

  // Update messages when current conversation changes
  useEffect(() => {
    if (currentConversationId) {
      const currentConv = conversations.find(c => c.id === currentConversationId)
      if (currentConv) {
        setMessages(currentConv.messages)
      }
    } else {
      setMessages([])
    }
  }, [currentConversationId, conversations])

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  const handleNewConversation = () => {
    const newId = uuidv4()
    const newConversation: Conversation = {
      id: newId,
      title: "New Conversation",
      lastMessage: "Start a new conversation",
      timestamp: Date.now(),
      messages: [],
    }
    
    setConversations(prev => [newConversation, ...prev])
    setCurrentConversationId(newId)
    setMessages([])
    setSidebarOpen(false)
    setUploadedFiles([])
  }

  const handleSelectConversation = (id: string) => {
    setCurrentConversationId(id)
    const conversation = conversations.find(c => c.id === id)
    if (conversation) {
      setMessages(conversation.messages)
    }
    setSidebarOpen(false)
  }

  const handleDeleteConversation = (id: string) => {
    setConversations(prev => prev.filter(c => c.id !== id))
    
    if (currentConversationId === id) {
      const remaining = conversations.filter(c => c.id !== id)
      if (remaining.length > 0) {
        setCurrentConversationId(remaining[0].id)
        setMessages(remaining[0].messages)
      } else {
        setCurrentConversationId(null)
        setMessages([])
      }
    }
  }

  const handleRenameConversation = (id: string, newTitle: string) => {
    setConversations(prev => 
      prev.map(c => 
        c.id === id ? { ...c, title: newTitle } : c
      )
    )
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value)
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!input.trim() || isLoading) return

    // Create a new conversation if none exists
    if (!currentConversationId) {
      handleNewConversation()
    }

    const userMessage: Message = {
      id: uuidv4(),
      role: "user",
      content: input,
      createdAt: new Date()
    }

    // Update messages state
    setMessages(prev => [...prev, userMessage])
    setInput("")
    setIsLoading(true)
    setError(null)

    // Update conversation
    setConversations(prev => 
      prev.map(c => 
        c.id === currentConversationId 
          ? { 
              ...c, 
              messages: [...c.messages, userMessage],
              lastMessage: input.slice(0, 60) + (input.length > 60 ? "..." : ""),
              timestamp: Date.now(),
              title: c.messages.length === 0 ? generateTitle(input) : c.title
            } 
          : c
      )
    )

    try {
      // Call the API
      const response = await fetch("/api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          messages: [...messages, userMessage],
          uploadedFiles: uploadedFiles,
          model: selectedModel
        }),
      })

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`)
      }

      const data = await response.json()

      const assistantMessage: Message = {
        id: uuidv4(),
        role: "assistant",
        content: data.content,
        createdAt: new Date()
      }

      // Update messages state
      setMessages(prev => [...prev, assistantMessage])

      // Update conversation
      setConversations(prev => 
        prev.map(c => 
          c.id === currentConversationId 
            ? { 
                ...c, 
                messages: [...c.messages, assistantMessage],
                lastMessage: `AI: ${data.content.slice(0, 60)}${data.content.length > 60 ? "..." : ""}`,
                timestamp: Date.now()
              } 
            : c
        )
      )
    } catch (err) {
      console.error("Chat error:", err)
      setError(err instanceof Error ? err : new Error("Unknown error occurred"))
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to get a response",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleFileUpload = (files: UploadedFile[]) => {
    setUploadedFiles(prev => [...prev, ...files])
    setShowFileUpload(false)
    
    toast({
      title: "Files uploaded",
      description: `${files.length} file${files.length !== 1 ? 's' : ''} ready for analysis`,
    })
  }

  const removeFile = (id: string) => {
    setUploadedFiles(prev => prev.filter(file => file.id !== id))
  }

  const handleModelChange = (model: "gemini" | "grok") => {
    setSelectedModel(model)
    toast({
      title: "Model changed",
      description: `Now using ${model === "gemini" ? "Google Gemini" : "Grok"} model`,
    })
  }

  // Generate a title from the first message
  const generateTitle = (content: string) => {
    const maxLength = 30
    return content.length > maxLength 
      ? content.substring(0, maxLength) + "..." 
      : content
  }

  return (
    <div className="flex h-screen overflow-hidden bg-gray-50 dark:bg-gray-900">
      {/* Sidebar */}
      <ChatSidebar
        open={sidebarOpen}
        onOpenChange={setSidebarOpen}
        conversations={conversations}
        currentConversationId={currentConversationId}
        onSelectConversation={handleSelectConversation}
        onNewConversation={handleNewConversation}
        onDeleteConversation={handleDeleteConversation}
        onRenameConversation={handleRenameConversation}
        user={user}
      />

      {/* Main content */}
      <div className="flex flex-col flex-1 h-full overflow-hidden">
        <ChatHeader 
          onToggleSidebar={() => setSidebarOpen(!sidebarOpen)} 
          onNewChat={handleNewConversation}
          selectedModel={selectedModel}
          onModelChange={handleModelChange}
          user={user}
        />
        
        <ChatInterface
          messages={messages}
          input={input}
          handleInputChange={handleInputChange}
          handleSubmit={handleSubmit}
          isLoading={isLoading}
          error={error}
          uploadedFiles={uploadedFiles}
          onRemoveFile={removeFile}
          onShowFileUpload={() => setShowFileUpload(true)}
          selectedModel={selectedModel}
          messagesEndRef={messagesEndRef}
        />
      </div>

      {/* File upload dialog */}
      <DocumentUpload
        open={showFileUpload}
        onOpenChange={setShowFileUpload}
        onFilesUploaded={handleFileUpload}
      />
    </div>
  )
}