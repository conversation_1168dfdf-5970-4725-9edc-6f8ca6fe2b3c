import { NextResponse } from "next/server";
import axios from "axios";

export const maxDuration = 30;

// OpenRouter API configuration
const GEMMA_API_KEY = "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";
const DEEPSEEK_API_KEY = "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";
const OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1";

// Define Message type
interface Message {
  role: string;
  content: string;
}

interface UploadedFile {
  name: string;
  content: string;
  type: string;
}

export async function POST(req: Request) {
  try {
    const { messages, uploadedFiles, selectedModel = "google/gemma-2-9b-it" } = await req.json();

    // Select the correct API key based on the model
    const getApiKey = (model: string) => {
      if (model.includes("deepseek")) {
        return DEEPSEEK_API_KEY;
      } else if (model.includes("gemma")) {
        return GEMMA_API_KEY;
      }
      return GEMMA_API_KEY; // Default to Gemma key
    };

    const apiKey = getApiKey(selectedModel);

    // Get the last user message
    const lastUserMessage = messages.filter((m: Message) => m.role === "user").pop()?.content || "Hello";

    // Enhanced system message for ChatNova - model specific
    const getModelName = (model: string) => {
      if (model.includes("deepseek")) return "DeepSeek";
      if (model.includes("gemma")) return "Gemma 3";
      return "AI";
    };

    let systemPrompt = `You are ChatNova, an advanced AI assistant powered by ${getModelName(selectedModel)}. You provide intelligent, detailed responses with a friendly and professional tone. You excel at reasoning, analysis, and creative problem-solving.

Key guidelines:
- Be conversational, helpful, and engaging
- Provide detailed, well-structured responses
- Use step-by-step thinking for complex queries
- Be honest when you don't know something
- Show your reasoning process when helpful
- Use clear, accessible language
- Be creative and innovative in your solutions`;

    // Enhanced PDF content handling
    if (uploadedFiles && uploadedFiles.length > 0) {
      systemPrompt += `\n\n=== UPLOADED DOCUMENTS ===\n`;
      uploadedFiles.forEach((file: UploadedFile, index: number) => {
        systemPrompt += `\n📄 DOCUMENT ${index + 1}: "${file.name}"\n`;
        systemPrompt += `📝 CONTENT:\n${file.content}\n`;
        systemPrompt += `${'='.repeat(50)}\n`;
      });
      systemPrompt += `\n🔍 IMPORTANT INSTRUCTIONS FOR DOCUMENT ANALYSIS:
- You have full access to the content of the uploaded documents above
- When users ask questions, carefully analyze the document content to provide accurate answers
- Always cite the specific document name when referencing information
- If the user asks about something not in the documents, clearly state that
- Provide detailed quotes and page references when possible
- Summarize key points from the documents when asked
- Compare information across multiple documents if relevant
- Be specific about which document contains which information`;
    }

    // Build conversation context
    let conversationContext = "";
    if (messages.length > 1) {
      conversationContext = "\n\n=== CONVERSATION HISTORY ===\n";
      messages.slice(-5).forEach((msg: Message) => { // Last 5 messages for context
        conversationContext += `${msg.role.toUpperCase()}: ${msg.content}\n`;
      });
      conversationContext += "=".repeat(50) + "\n";
    }

    // Prepare the enhanced prompt with better structure
    const fullPrompt = `${systemPrompt}${conversationContext}

🎯 CURRENT USER QUERY: ${lastUserMessage}

Please provide a comprehensive response based on:
1. The uploaded documents (if any)
2. The conversation context
3. Your knowledge and reasoning capabilities

Remember to cite sources when using document information!`;

    // Prepare messages for OpenRouter API
    const openRouterMessages = [
      {
        role: "system",
        content: systemPrompt
      },
      ...messages.slice(-5).map((msg: Message) => ({
        role: msg.role === "user" ? "user" : "assistant",
        content: msg.content
      }))
    ];

    // Add document context if available
    if (uploadedFiles && uploadedFiles.length > 0) {
      const documentContext = uploadedFiles.map((file: UploadedFile, index: number) => 
        `Document ${index + 1} (${file.name}):\n${file.content}`
      ).join('\n\n');
      
      openRouterMessages.unshift({
        role: "system",
        content: `You have access to the following documents:\n\n${documentContext}\n\nUse this information to answer user questions accurately.`
      });
    }

    // Make request to OpenRouter API
    const response = await axios.post(
      `${OPENROUTER_BASE_URL}/chat/completions`,
      {
        model: selectedModel,
        messages: openRouterMessages,
        max_tokens: 4000,
        temperature: 0.7,
        top_p: 0.9,
        frequency_penalty: 0,
        presence_penalty: 0,
        stream: false
      },
      {
        headers: {
          "Authorization": `Bearer ${apiKey}`,
          "Content-Type": "application/json",
          "HTTP-Referer": "https://chatnova.dev",
          "X-Title": "ChatNova"
        },
        timeout: 30000
      }
    );

    const responseText = response.data.choices[0]?.message?.content || "I apologize, but I couldn't generate a response. Please try again.";

    return NextResponse.json({
      role: "assistant",
      content: responseText,
      model: selectedModel
    });

  } catch (error: any) {
    console.error("OpenRouter API error:", error);
    
    // Enhanced error handling
    let errorMessage = "Failed to generate response";
    let errorDetails = "Unknown error";

    if (error.response) {
      errorMessage = `OpenRouter API error: ${error.response.status}`;
      errorDetails = error.response.data?.error?.message || error.response.statusText;
    } else if (error.request) {
      errorMessage = "Network error";
      errorDetails = "Unable to reach OpenRouter API";
    } else {
      errorDetails = error.message;
    }

    return NextResponse.json({
      error: errorMessage,
      details: errorDetails,
      fallback: true
    }, { status: 500 });
  }
}
