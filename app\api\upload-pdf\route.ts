import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import { join } from 'path';
import * as os from 'os';
import { v4 as uuidv4 } from 'uuid';

// Advanced PDF text extraction using PDF.js
async function extractPDFText(buffer: Buffer, filename: string): Promise<string> {
  try {
    // Dynamically import pdfjs-dist with proper configuration
    const pdfjsLib = await import('pdfjs-dist/legacy/build/pdf.min.mjs');

    // Configure PDF.js for serverless environment
    pdfjsLib.GlobalWorkerOptions.workerSrc = 'pdfjs-dist/legacy/build/pdf.worker.min.mjs';

    // Load the PDF document with better error handling
    const loadingTask = pdfjsLib.getDocument({
      data: new Uint8Array(buffer),
      useSystemFonts: true,
      disableFontFace: true,
      verbosity: 0,
      cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@4.0.379/cmaps/',
      cMapPacked: true,
      standardFontDataUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@4.0.379/standard_fonts/',
    });

    const pdf = await loadingTask.promise;
    let fullText = '';

    // Extract text from each page with better error handling
    for (let pageNum = 1; pageNum <= Math.min(pdf.numPages, 50); pageNum++) { // Limit to 50 pages for performance
      try {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();

        // Combine text items with proper spacing and line breaks
        const pageText = textContent.items
          .map((item: any) => {
            if ('str' in item && item.str.trim()) {
              return item.str;
            }
            return '';
          })
          .filter(text => text.length > 0)
          .join(' ');

        if (pageText.trim()) {
          fullText += `\n--- Page ${pageNum} ---\n${pageText}\n`;
        }

        // Clean up the page to free memory
        page.cleanup();
      } catch (pageError) {
        console.error(`Error extracting text from page ${pageNum}:`, pageError);
        fullText += `\n--- Page ${pageNum} ---\n[Error extracting text from this page]\n`;
      }
    }

    // Clean up the extracted text
    fullText = fullText
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n')
      .trim();

    if (!fullText || fullText.length < 10) {
      // Try OCR fallback for image-based PDFs
      return await tryOCRExtraction(buffer, filename);
    }

    return fullText;

  } catch (error) {
    console.error('PDF.js extraction error:', error);

    // Fallback to basic extraction method
    try {
      return await basicPDFExtraction(buffer, filename);
    } catch (fallbackError) {
      console.error('Fallback extraction also failed:', fallbackError);
      return `PDF "${filename}" could not be processed. The file may be corrupted, password-protected, or use an unsupported format. Error: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }
}

// OCR fallback for image-based PDFs
async function tryOCRExtraction(buffer: Buffer, filename: string): Promise<string> {
  try {
    // Import Tesseract.js for OCR processing
    const Tesseract = await import('tesseract.js');

    // Convert PDF to images first (simplified approach)
    // In a full implementation, you'd use PDF.js to render pages as images
    // For now, we'll try OCR on the PDF buffer directly (limited effectiveness)

    console.log(`Attempting OCR extraction for ${filename}...`);

    // Create a worker for OCR processing
    const worker = await Tesseract.createWorker('eng', 1, {
      logger: m => console.log('OCR Progress:', m)
    });

    try {
      // Convert buffer to base64 for Tesseract
      const base64Data = `data:application/pdf;base64,${buffer.toString('base64')}`;

      // Perform OCR recognition
      const { data: { text } } = await worker.recognize(base64Data);

      await worker.terminate();

      if (text && text.trim().length > 10) {
        return `OCR Extracted Text from "${filename}":\n\n${text.trim()}`;
      } else {
        return `PDF "${filename}" was processed with OCR but minimal text was extracted. This could be due to poor image quality, complex formatting, or the PDF containing primarily non-text content.`;
      }
    } catch (ocrError) {
      await worker.terminate();
      throw ocrError;
    }
  } catch (error) {
    console.error('OCR extraction error:', error);
    return `PDF "${filename}" appears to be image-based or scanned. OCR processing failed: ${error instanceof Error ? error.message : 'Unknown OCR error'}. This document may require manual text extraction or a different OCR approach.`;
  }
}

// Fallback basic PDF extraction method
async function basicPDFExtraction(buffer: Buffer, filename: string): Promise<string> {
  const pdfString = buffer.toString('binary');
  
  // Look for text patterns in PDF structure
  const textRegex = /BT\s*.*?ET/gs;
  const matches = pdfString.match(textRegex);
  
  if (!matches) {
    return `PDF "${filename}" appears to be image-based or uses a complex format.`;
  }
  
  let extractedText = '';
  
  for (const match of matches) {
    const textContent = match.match(/\((.*?)\)/g) || match.match(/\[(.*?)\]/g);
    if (textContent) {
      for (const text of textContent) {
        const cleanText = text.replace(/[()[\]]/g, '').trim();
        if (cleanText && cleanText.length > 1) {
          extractedText += cleanText + ' ';
        }
      }
    }
  }
  
  return extractedText.replace(/\s+/g, ' ').trim();
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }

    // Check file type
    const fileType = file.type;
    const validTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ];

    if (!validTypes.includes(fileType)) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Unsupported file type: ${fileType}. Supported types are PDF, DOC, DOCX, and TXT.` 
        },
        { status: 400 }
      );
    }

    // Create a temporary file
    const tempDir = os.tmpdir();
    const tempFilePath = join(tempDir, `${uuidv4()}-${file.name}`);
    
    // Convert file to buffer and save to temp location
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await fs.writeFile(tempFilePath, buffer);

    // Process the file based on type
    let content = '';
    
    if (fileType === 'application/pdf') {
      try {
        // Alternative PDF text extraction approach for serverless environments
        content = await extractPDFText(buffer, file.name);
        
        // If no text was extracted, provide a helpful message
        if (!content || content.trim().length === 0) {
          content = `PDF file "${file.name}" was processed but no readable text content was found. This could be because the PDF contains only images, is password-protected, or uses a format that doesn't support text extraction.`;
        }
      } catch (pdfError) {
        console.error('PDF parsing error:', pdfError);
        content = `Error extracting content from PDF "${file.name}": ${pdfError instanceof Error ? pdfError.message : 'Unknown error occurred during PDF processing'}`;
      }
    } else if (fileType.includes('word')) {
      // For DOC/DOCX files
      content = `Extracted content from Word document: ${file.name}\n\nThis is simulated content extraction. In a production environment, you would use a library like mammoth or docx to extract the actual text content from the Word document.`;
    } else if (fileType === 'text/plain') {
      // For TXT files, we can read the content directly
      content = buffer.toString('utf-8');
    }

    // Clean up the temp file
    try {
      await fs.unlink(tempFilePath);
    } catch (error) {
      console.error('Error deleting temp file:', error);
    }

    return NextResponse.json({
      success: true,
      content,
      filename: file.name,
      fileType,
      fileSize: file.size,
      extractedAt: new Date().toISOString(),
      contentLength: content.length,
    });
  } catch (error) {
    console.error('Error processing file:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to process file' },
      { status: 500 }
    );
  }
}