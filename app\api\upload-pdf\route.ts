import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import { join } from 'path';
import * as os from 'os';
import { v4 as uuidv4 } from 'uuid';

// Enhanced PDF text extraction with better error handling
async function extractPDFText(buffer: Buffer, filename: string): Promise<string> {
  try {
    // First attempt: Use PDF.js for text extraction
    const extractedText = await extractWithPDFJS(buffer);
    
    // If PDF.js extraction was successful, return the result
    if (extractedText && extractedText.trim().length > 50) {
      return extractedText;
    }
    
    // Second attempt: Try advanced extraction for complex PDFs
    const advancedExtractedText = await extractWithAdvancedTechniques(buffer, filename);
    
    // If advanced extraction was successful, return the result
    if (advancedExtractedText && advancedExtractedText.trim().length > 50) {
      return advancedExtractedText;
    }
    
    // Third attempt: Try OCR for image-based PDFs
    try {
      // Only import Tesseract.js if needed (for OCR)
      const { createWorker } = await import('tesseract.js');
      
      console.log(`Attempting OCR extraction for "${filename}"`);
      
      // Create a temporary file for the first page image
      const tempDir = os.tmpdir();
      const tempImagePath = join(tempDir, `${uuidv4()}-page1.png`);
      
      // Extract the first page as an image using PDF.js
      const pdfjsLib = await import('pdfjs-dist');
      
      // Configure PDF.js worker
      const pdfjsWorker = await import('pdfjs-dist/build/pdf.worker.mjs');
      pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;
      
      // Load the PDF
      const loadingTask = pdfjsLib.getDocument({
        data: new Uint8Array(buffer),
        useSystemFonts: true,
      });
      
      const pdf = await loadingTask.promise;
      
      // Get the first page
      const page = await pdf.getPage(1);
      
      // Render the page to a canvas (Node.js environment)
      const viewport = page.getViewport({ scale: 2.0 }); // Higher scale for better OCR
      
      // Use a virtual canvas in Node.js
      const { createCanvas } = await import('canvas');
      const canvas = createCanvas(viewport.width, viewport.height);
      const context = canvas.getContext('2d');
      
      // Render the PDF page to the canvas
      await page.render({
        canvasContext: context,
        viewport: viewport,
      }).promise;
      
      // Save the canvas as an image
      const buffer = canvas.toBuffer('image/png');
      await fs.writeFile(tempImagePath, buffer);
      
      // Initialize Tesseract worker
      const worker = await createWorker();
      
      // Set language to English
      await worker.loadLanguage('eng');
      await worker.initialize('eng');
      
      // Recognize text from the image
      const { data } = await worker.recognize(tempImagePath);
      
      // Clean up
      await worker.terminate();
      try {
        await fs.unlink(tempImagePath);
      } catch (error) {
        console.error('Error deleting temp image file:', error);
      }
      
      // If OCR was successful, return the result
      if (data.text && data.text.trim().length > 50) {
        return `--- OCR Extracted Text ---\n${data.text.trim()}\n\nNote: This PDF appears to be image-based or uses a complex format. Text was extracted using OCR technology.`;
      }
      
    } catch (ocrError) {
      console.error('OCR extraction error:', ocrError);
      // Continue to fallback methods if OCR fails
    }
    
    // If all extraction methods failed, return a helpful message
    return `PDF "${filename}" appears to be image-based or uses a complex format. The system attempted multiple extraction methods including OCR, but couldn't extract meaningful text. This may be due to:
    
1. The PDF contains mostly images or scanned content
2. The PDF uses custom fonts or complex formatting
3. The PDF may be password-protected or encrypted
4. The PDF may use non-standard text encoding

For best results, try uploading a PDF with selectable text or provide a text-based version of this document.`;
    
  } catch (error) {
    console.error('PDF extraction error:', error);
    return `Error processing PDF "${filename}": ${error instanceof Error ? error.message : 'Unknown error occurred during processing'}`;
  }
}

// PDF.js extraction method
async function extractWithPDFJS(buffer: Buffer): Promise<string> {
  try {
    // Dynamically import pdfjs-dist
    const pdfjsLib = await import('pdfjs-dist');
    
    // Configure PDF.js worker
    const pdfjsWorker = await import('pdfjs-dist/build/pdf.worker.mjs');
    pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;
    
    // Enhanced PDF.js options for better text extraction
    const loadingTask = pdfjsLib.getDocument({
      data: new Uint8Array(buffer),
      useSystemFonts: true,
      disableFontFace: false, // Try with font support
    });
    
    const pdf = await loadingTask.promise;
    let fullText = '';
    
    // Extract text from each page with enhanced options
    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
      try {
        const page = await pdf.getPage(pageNum);
        
        // Try to get text content with better layout preservation
        const textContent = await page.getTextContent({
          normalizeWhitespace: true,
          disableCombineTextItems: false,
        });
        
        // Process text items with position information for better layout
        const textItems = textContent.items;
        let lastY = null;
        let pageText = '';
        
        for (const item of textItems) {
          if ('str' in item && item.str.trim()) {
            // Add newline if y-position changes significantly
            if (lastY !== null && Math.abs(lastY - item.transform[5]) > 5) {
              pageText += '\n';
            }
            
            pageText += item.str + ' ';
            lastY = item.transform[5];
          }
        }
        
        if (pageText.trim()) {
          fullText += `\n--- Page ${pageNum} ---\n${pageText.trim()}\n`;
        }
      } catch (pageError) {
        console.error(`Error extracting text from page ${pageNum}:`, pageError);
      }
    }
    
    // Clean up the extracted text
    fullText = fullText
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n')
      .trim();
    
    return fullText;
  } catch (error) {
    console.error('PDF.js extraction error:', error);
    return '';
  }
}

// Advanced extraction techniques for complex PDFs
async function extractWithAdvancedTechniques(buffer: Buffer, filename: string): Promise<string> {
  try {
    // Try multiple approaches for text extraction
    
    // 1. Try with different PDF.js configurations
    const pdfjsLib = await import('pdfjs-dist');
    
    // Configure PDF.js worker
    const pdfjsWorker = await import('pdfjs-dist/build/pdf.worker.mjs');
    pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;
    
    // Try with different rendering parameters
    const loadingTask = pdfjsLib.getDocument({
      data: new Uint8Array(buffer),
      useSystemFonts: true,
      disableFontFace: false,
    });
    
    const pdf = await loadingTask.promise;
    let fullText = '';
    
    // Try a different approach to extract text
    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
      try {
        const page = await pdf.getPage(pageNum);
        
        // Get text content with different options
        const textContent = await page.getTextContent({
          normalizeWhitespace: false,
          disableCombineTextItems: true,
        });
        
        // Try to extract text with more context about positioning
        let lastY = -1;
        let pageText = '';
        
        // Sort items by vertical position to better preserve reading order
        const sortedItems = textContent.items.sort((a: any, b: any) => {
          if (!('transform' in a) || !('transform' in b)) return 0;
          // Sort primarily by y-coordinate (transform[5]), then by x-coordinate (transform[4])
          return a.transform[5] !== b.transform[5] 
            ? b.transform[5] - a.transform[5] 
            : a.transform[4] - b.transform[4];
        });
        
        for (const item of sortedItems) {
          if ('str' in item && item.str.trim()) {
            // Check if we're on a new line
            const currentY = item.transform[5];
            if (lastY !== -1 && Math.abs(currentY - lastY) > 2) {
              pageText += '\n';
            } else if (lastY !== -1 && Math.abs(currentY - lastY) <= 2) {
              // Same line, add space if needed
              if (!pageText.endsWith(' ')) {
                pageText += ' ';
              }
            }
            
            pageText += item.str;
            lastY = currentY;
          }
        }
        
        if (pageText.trim()) {
          fullText += `\n--- Page ${pageNum} ---\n${pageText.trim()}\n`;
        }
      } catch (pageError) {
        console.error(`Error in advanced extraction for page ${pageNum}:`, pageError);
      }
    }
    
    // Clean up and return if we got meaningful text
    if (fullText.trim().length > 50) {
      return fullText
        .replace(/\s+/g, ' ')
        .replace(/\n\s*\n/g, '\n')
        .trim();
    }
    
    // 2. Fallback to basic extraction as last resort
    return await basicPDFExtraction(buffer, filename);
    
  } catch (error) {
    console.error('Advanced extraction error:', error);
    return '';
  }
}

// Fallback basic PDF extraction method
async function basicPDFExtraction(buffer: Buffer, filename: string): Promise<string> {
  const pdfString = buffer.toString('binary');
  
  // Look for text patterns in PDF structure
  const textRegex = /BT\s*.*?ET/gs;
  const matches = pdfString.match(textRegex);
  
  if (!matches) {
    return `PDF "${filename}" appears to be image-based or uses a complex format.`;
  }
  
  let extractedText = '';
  
  for (const match of matches) {
    const textContent = match.match(/\((.*?)\)/g) || match.match(/\[(.*?)\]/g);
    if (textContent) {
      for (const text of textContent) {
        const cleanText = text.replace(/[()[\]]/g, '').trim();
        if (cleanText && cleanText.length > 1) {
          extractedText += cleanText + ' ';
        }
      }
    }
  }
  
  return extractedText.replace(/\s+/g, ' ').trim();
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }

    // Check file type
    const fileType = file.type;
    const validTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ];

    if (!validTypes.includes(fileType)) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Unsupported file type: ${fileType}. Supported types are PDF, DOC, DOCX, and TXT.` 
        },
        { status: 400 }
      );
    }

    // Create a temporary file
    const tempDir = os.tmpdir();
    const tempFilePath = join(tempDir, `${uuidv4()}-${file.name}`);
    
    // Convert file to buffer and save to temp location
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await fs.writeFile(tempFilePath, buffer);

    // Process the file based on type
    let content = '';
    
    if (fileType === 'application/pdf') {
      try {
        // Use enhanced PDF extraction with OCR capabilities
        content = await extractPDFText(buffer, file.name);
        
        // If no text was extracted, provide a helpful message
        if (!content || content.trim().length === 0) {
          content = `PDF file "${file.name}" was processed but no readable text content was found. This could be because the PDF contains only images, is password-protected, or uses a format that doesn't support text extraction.`;
        }
        
        // Add metadata about extraction method
        if (content.includes('OCR Extracted Text')) {
          console.log(`Successfully extracted text from "${file.name}" using OCR`);
        } else if (content.includes('appears to be image-based')) {
          console.log(`Could not extract text from "${file.name}" - image-based PDF detected`);
        } else {
          console.log(`Successfully extracted text from "${file.name}" using standard methods`);
        }
      } catch (pdfError) {
        console.error('PDF parsing error:', pdfError);
        content = `Error extracting content from PDF "${file.name}": ${pdfError instanceof Error ? pdfError.message : 'Unknown error occurred during PDF processing'}`;
      }
    } else if (fileType.includes('word')) {
      // For DOC/DOCX files
      content = `Extracted content from Word document: ${file.name}\n\nThis is simulated content extraction. In a production environment, you would use a library like mammoth or docx to extract the actual text content from the Word document.`;
    } else if (fileType === 'text/plain') {
      // For TXT files, we can read the content directly
      content = buffer.toString('utf-8');
    }

    // Clean up the temp file
    try {
      await fs.unlink(tempFilePath);
    } catch (error) {
      console.error('Error deleting temp file:', error);
    }

    return NextResponse.json({
      success: true,
      content,
      filename: file.name,
      fileType,
      fileSize: file.size,
      extractedAt: new Date().toISOString(),
      contentLength: content.length,
    });
  } catch (error) {
    console.error('Error processing file:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to process file' },
      { status: 500 }
    );
  }
}