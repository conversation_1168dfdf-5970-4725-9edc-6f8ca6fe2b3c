 "use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { 
  Menu, 
  Moon, 
  Sun, 
  Plus, 
  LogOut, 
  Settings, 
  User, 
  HelpCircle,
  Sparkles,
  Zap
} from "lucide-react"
import { useTheme } from "next-themes"
import { useAuth } from "@/components/auth-provider"
import { useToast } from "@/components/ui/use-toast"

interface User {
  id: string
  name: string
  email: string
  image?: string
}

interface ChatHeaderProps {
  onToggleSidebar: () => void
  onNewChat: () => void
  selectedModel: "gemini" | "grok"
  onModelChange: (model: "gemini" | "grok") => void
  user: User | null
}

export function ChatHeader({ 
  onToggleSidebar, 
  onNewChat, 
  selectedModel, 
  onModelChange,
  user 
}: ChatHeaderProps) {
  const { theme, setTheme } = useTheme()
  const { logout } = useAuth()
  const { toast } = useToast()
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)

  const handleLogout = () => {
    logout()
    toast({
      title: "Logged out",
      description: "You have been successfully logged out",
    })
  }

  return (
    <header className="sticky top-0 z-50 border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 h-14">
      <div className="flex h-full items-center px-4">
        {/* Left section: Mobile menu button and New Chat */}
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggleSidebar}
            className="md:hidden"
            aria-label="Toggle sidebar"
          >
            <Menu className="h-5 w-5" />
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={onNewChat}
            className="hidden md:flex items-center gap-1.5 h-9"
          >
            <Plus className="h-4 w-4" />
            <span>New chat</span>
          </Button>
        </div>

        {/* Center section: Logo (mobile only) */}
        <div className="flex-1 flex justify-center md:justify-start">
          <div className="md:hidden flex items-center gap-1.5">
            <Sparkles className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            <span className="font-semibold text-lg">ChatNova</span>
          </div>
        </div>

        {/* Right section: Model selector, Theme toggle, User menu */}
        <div className="flex items-center gap-2">
          {/* Model selector */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="outline" 
                size="sm" 
                className="hidden sm:flex items-center gap-1.5 h-9"
              >
                {selectedModel === "gemini" ? (
                  <>
                    <Sparkles className="h-4 w-4 text-purple-500" />
                    <span>Gemini</span>
                  </>
                ) : (
                  <>
                    <Zap className="h-4 w-4 text-yellow-500" />
                    <span>Grok</span>
                  </>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem 
                onClick={() => onModelChange("gemini")}
                className={selectedModel === "gemini" ? "bg-blue-50 dark:bg-blue-900/20" : ""}
              >
                <Sparkles className="h-4 w-4 mr-2 text-purple-500" />
                <span>Google Gemini</span>
                {selectedModel === "gemini" && (
                  <span className="ml-auto text-blue-600 dark:text-blue-400">✓</span>
                )}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => onModelChange("grok")}
                className={selectedModel === "grok" ? "bg-blue-50 dark:bg-blue-900/20" : ""}
              >
                <Zap className="h-4 w-4 mr-2 text-yellow-500" />
                <span>Grok</span>
                {selectedModel === "grok" && (
                  <span className="ml-auto text-blue-600 dark:text-blue-400">✓</span>
                )}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Theme toggle */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
            className="h-9 w-9"
            aria-label="Toggle theme"
          >
            <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
            <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          </Button>

          {/* User menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-9 w-9 rounded-full">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user?.image} alt={user?.name || "User"} />
                  <AvatarFallback>
                    {user?.name?.charAt(0) || user?.email?.charAt(0) || "U"}
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <div className="flex items-center justify-start gap-2 p-2">
                <div className="flex flex-col space-y-0.5">
                  <p className="text-sm font-medium">{user?.name}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">{user?.email}</p>
                </div>
              </div>
              <DropdownMenuSeparator />
              <Link href="/settings" className="w-full">
                <DropdownMenuItem>
                  <User className="h-4 w-4 mr-2" />
                  <span>Profile</span>
                </DropdownMenuItem>
              </Link>
              <Link href="/settings" className="w-full">
                <DropdownMenuItem>
                  <Settings className="h-4 w-4 mr-2" />
                  <span>Settings</span>
                </DropdownMenuItem>
              </Link>
              <DropdownMenuItem>
                <HelpCircle className="h-4 w-4 mr-2" />
                <span>Help</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
}