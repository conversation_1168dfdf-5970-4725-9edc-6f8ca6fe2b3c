"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>rollArea } from "@/components/ui/scroll-area"
import { FileText, Image, File, X, Eye } from "lucide-react"
import { 
  <PERSON><PERSON>, 
  <PERSON>alogContent, 
  <PERSON>alogHeader, 
  <PERSON>alog<PERSON><PERSON><PERSON>, 
  DialogTrigger 
} from "@/components/ui/dialog"
import { useState } from "react"

interface UploadedFile {
  id: string
  name: string
  type: string
  size?: number
  content: string
  url?: string
}

interface UploadedFilesProps {
  files: UploadedFile[]
  onRemoveFile: (id: string) => void
}

export function UploadedFiles({ files, onRemoveFile }: UploadedFilesProps) {
  const [selectedFile, setSelectedFile] = useState<UploadedFile | null>(null)
  const [dialogOpen, setDialogOpen] = useState(false)

  const handleViewFile = (file: UploadedFile) => {
    setSelectedFile(file)
    setDialogOpen(true)
  }

  const getFileIcon = (type: string) => {
    if (type.startsWith("image/")) {
      return <Image className="h-4 w-4 text-blue-500" />
    } else if (type === "application/pdf") {
      return <FileText className="h-4 w-4 text-red-500" />
    } else if (type.includes("word") || type === "text/plain") {
      return <FileText className="h-4 w-4 text-blue-500" />
    } else {
      return <File className="h-4 w-4 text-gray-500" />
    }
  }

  const getFilePreview = (file: UploadedFile) => {
    if (file.type.startsWith("image/") && file.url) {
      return (
        <div className="flex justify-center p-4">
          <img 
            src={file.url} 
            alt={file.name} 
            className="max-w-full max-h-[70vh] object-contain rounded-lg shadow-md" 
          />
        </div>
      )
    } else {
      return (
        <ScrollArea className="h-[70vh] w-full p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <pre className="text-sm whitespace-pre-wrap font-mono p-4">
            {file.content}
          </pre>
        </ScrollArea>
      )
    }
  }

  return (
    <div className="space-y-2">
      <div className="flex flex-wrap gap-2">
        {files.map((file) => (
          <div 
            key={file.id}
            className="flex items-center gap-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg px-3 py-2 text-sm"
          >
            {getFileIcon(file.type)}
            <span className="truncate max-w-[150px]">{file.name}</span>
            
            <div className="flex items-center gap-1 ml-1">
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                onClick={() => handleViewFile(file)}
              >
                <Eye className="h-3 w-3 text-gray-500 dark:text-gray-400" />
              </Button>
              
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                onClick={() => onRemoveFile(file.id)}
              >
                <X className="h-3 w-3 text-gray-500 dark:text-gray-400" />
              </Button>
            </div>
          </div>
        ))}
      </div>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {selectedFile && getFileIcon(selectedFile.type)}
              {selectedFile?.name}
            </DialogTitle>
          </DialogHeader>
          
          {selectedFile && getFilePreview(selectedFile)}
        </DialogContent>
      </Dialog>
    </div>
  )
}