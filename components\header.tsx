"use client"

import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Menu, <PERSON>, Sun, Globe, Sparkles } from "lucide-react"
import { useTheme } from "next-themes"
import { useLanguage } from "@/components/language-provider"

interface HeaderProps {
  onToggleSidebar: () => void
}

export function Header({ onToggleSidebar }: HeaderProps) {
  const { theme, setTheme } = useTheme()
  const { language, setLanguage, t } = useLanguage()

  return (
    <header className="border-b border-white/10 bg-black/40 backdrop-blur-xl relative">
      <div className="absolute inset-0 bg-gradient-to-b from-black/20 to-transparent pointer-events-none"></div>
      <div className="flex h-16 items-center px-6 relative z-10">
        {/* Mobile menu button */}
        <Button
          variant="ghost"
          size="icon"
          onClick={onToggleSidebar}
          className="md:hidden hover:bg-white/10 text-white mr-3 h-10 w-10 rounded-xl backdrop-blur-md border border-white/10 hover:border-white/20 transition-all duration-300"
        >
          <Menu className="h-5 w-5" />
        </Button>

        {/* Enhanced title with gradient */}
        <div className="flex-1 flex items-center justify-center md:justify-start">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center shadow-lg shadow-blue-500/20 mr-3">
              <Sparkles className="h-4 w-4 text-white" />
            </div>
            <h1 className="text-xl font-bold bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent">
              ChatNova
            </h1>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex items-center gap-3">
          {/* Theme toggle with enhanced styling */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
            className="hover:bg-white/10 text-white h-10 w-10 rounded-xl backdrop-blur-md border border-white/10 hover:border-white/20 transition-all duration-300"
          >
            <Sun className="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
            <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          </Button>
          
          {/* Language dropdown with enhanced styling */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                size="icon" 
                className="hover:bg-white/10 text-white h-10 w-10 rounded-xl backdrop-blur-md border border-white/10 hover:border-white/20 transition-all duration-300"
              >
                <Globe className="h-5 w-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="bg-black/80 backdrop-blur-xl border border-white/10 text-white">
              <DropdownMenuItem onClick={() => setLanguage('en')} className="hover:bg-white/10">
                English
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setLanguage('es')} className="hover:bg-white/10">
                Español
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setLanguage('fr')} className="hover:bg-white/10">
                Français
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
}