"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { She<PERSON>, SheetContent } from "@/components/ui/sheet"
import { Plus, MessageSquare } from "lucide-react"
import { useLanguage } from "@/components/language-provider"
import { cn } from "@/lib/utils"

interface Conversation {
  id: string
  title: string
  lastMessage: string
  timestamp: number
}

interface SidebarProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  conversations: Conversation[]
  currentConversation: string | null
  onSelectConversation: (id: string) => void
  onNewConversation: () => void
}

export function Sidebar({
  open,
  onOpenChange,
  conversations,
  currentConversation,
  onSelectConversation,
  onNewConversation,
}: SidebarProps) {
  const { t } = useLanguage()

  const SidebarContent = () => (
    <div className="flex flex-col h-full bg-gray-900 text-white">
      {/* ChatGPT-style header with new chat button */}
      <div className="p-3">
        <Button
          onClick={onNewConversation}
          className="w-full bg-transparent border border-gray-600 hover:bg-gray-700 text-white h-11 text-sm rounded-lg"
        >
          <Plus className="mr-2 h-4 w-4" />
          New chat
        </Button>
      </div>

      {/* ChatGPT-style conversations list */}
      <ScrollArea className="flex-1">
        <div className="px-3 space-y-1">
          {conversations.map((conversation) => (
            <Button
              key={conversation.id}
              variant="ghost"
              className={cn(
                "w-full justify-start text-left h-auto p-3 rounded-lg transition-all duration-200 group",
                currentConversation === conversation.id
                  ? "bg-gray-800 text-white"
                  : "hover:bg-gray-800 text-gray-300 hover:text-white",
              )}
              onClick={() => onSelectConversation(conversation.id)}
            >
              <div className="flex items-center gap-3 w-full">
                <MessageSquare className="h-4 w-4 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <div className="font-normal truncate text-sm">
                    {conversation.title}
                  </div>
                </div>
              </div>
            </Button>
          ))}

          {/* ChatGPT-style empty state */}
          {conversations.length === 0 && (
            <div className="text-center py-8 px-4 text-gray-400">
              <MessageSquare className="h-8 w-8 mx-auto mb-3 opacity-50" />
              <p className="text-sm">No conversations yet</p>
              <p className="text-xs mt-1 opacity-60">Start a new chat to begin</p>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* ChatGPT-style footer */}
      <div className="p-3 border-t border-gray-700">
        <div className="text-xs text-gray-400 text-center">
          ChatNova
        </div>
      </div>
    </div>
  )

  return (
    <>
      {/* Desktop Sidebar */}
      <div className="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0">
        <SidebarContent />
      </div>

      {/* Mobile Sidebar */}
      <Sheet open={open} onOpenChange={onOpenChange}>
        <SheetContent side="left" className="p-0 w-64 bg-transparent border-none">
          <SidebarContent />
        </SheetContent>
      </Sheet>
    </>
  )
}
