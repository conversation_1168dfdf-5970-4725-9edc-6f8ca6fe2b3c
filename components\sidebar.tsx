"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { She<PERSON>, SheetContent } from "@/components/ui/sheet"
import { Plus, MessageSquare, Sparkles, Settings, LogOut } from "lucide-react"
import { useLanguage } from "@/components/language-provider"
import { cn } from "@/lib/utils"

interface Conversation {
  id: string
  title: string
  lastMessage: string
  timestamp: number
}

interface SidebarProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  conversations: Conversation[]
  currentConversation: string | null
  onSelectConversation: (id: string) => void
  onNewConversation: () => void
}

export function Sidebar({
  open,
  onOpenChange,
  conversations,
  currentConversation,
  onSelectConversation,
  onNewConversation,
}: SidebarProps) {
  const { t } = useLanguage()

  const SidebarContent = () => (
    <div className="flex flex-col h-full bg-black/40 backdrop-blur-xl text-white border-r border-white/10 relative">
      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-white/5 via-transparent to-black/20 pointer-events-none"></div>
      
      {/* Enhanced Header with Glass Effect */}
      <div className="p-6 border-b border-white/10 relative z-10">
        <div className="mb-4">
          <div className="flex items-center mb-3">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center shadow-lg shadow-blue-500/20 mr-3">
              <Sparkles className="h-4 w-4 text-white" />
            </div>
            <h1 className="text-xl font-bold bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent">
              ChatNova
            </h1>
          </div>
          <p className="text-xs text-gray-400 mt-1">AI-Powered Assistant</p>
        </div>
        <Button
          onClick={onNewConversation}
          className="w-full bg-white/10 backdrop-blur-md border border-white/20 hover:bg-white/20 text-white h-12 text-sm rounded-xl transition-all duration-300 hover:scale-[1.02] hover:shadow-lg hover:shadow-white/10 group"
        >
          <Plus className="mr-2 h-4 w-4 group-hover:rotate-90 transition-transform duration-300" />
          New Conversation
        </Button>
      </div>

      {/* Enhanced Conversations List */}
      <ScrollArea className="flex-1 relative z-10 scrollbar-enhanced">
        <div className="px-4 py-4 space-y-2">
          {conversations.map((conversation, index) => (
            <Button
              key={conversation.id}
              variant="ghost"
              className={cn(
                "w-full justify-start text-left h-auto p-4 rounded-xl transition-all duration-300 group relative overflow-hidden",
                currentConversation === conversation.id
                  ? "bg-white/15 text-white border border-white/20 shadow-lg shadow-white/5"
                  : "hover:bg-white/10 text-gray-300 hover:text-white border border-transparent hover:border-white/10 hover:shadow-md hover:shadow-white/5",
              )}
              onClick={() => onSelectConversation(conversation.id)}
              style={{ animationDelay: `${index * 50}ms` }}
            >
              {/* Hover Effect Background */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              
              <div className="flex items-center gap-3 w-full relative z-10">
                <div className="w-8 h-8 bg-white/10 rounded-lg flex items-center justify-center group-hover:bg-white/20 transition-colors duration-300">
                  <MessageSquare className="h-4 w-4 text-gray-400 group-hover:text-white transition-colors duration-300" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="font-medium truncate text-sm group-hover:text-white transition-colors duration-300">
                    {conversation.title}
                  </div>
                  <div className="text-xs text-gray-500 truncate mt-1">
                    {new Date(conversation.timestamp).toLocaleDateString()}
                  </div>
                </div>
              </div>
            </Button>
          ))}

          {/* Enhanced Empty State */}
          {conversations.length === 0 && (
            <div className="text-center py-16 px-4">
              <div className="w-16 h-16 bg-white/10 rounded-2xl flex items-center justify-center mx-auto mb-4 backdrop-blur-md">
                <MessageSquare className="h-8 w-8 text-gray-400" />
              </div>
              <p className="text-sm font-medium text-white mb-2">No conversations yet</p>
              <p className="text-xs text-gray-400 leading-relaxed">
                Start a new conversation to begin your AI-powered journey
              </p>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Enhanced Footer */}
      <div className="p-6 border-t border-white/10 relative z-10">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <div className="text-xs text-gray-400 font-medium">
              Online & Ready
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" className="h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-white/10 rounded-lg">
              <Settings className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" className="h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-white/10 rounded-lg">
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <>
      {/* Desktop Sidebar - Full Height with Glass Effect */}
      <div className="hidden md:flex md:w-80 md:flex-col md:fixed md:inset-y-0 md:z-50">
        <SidebarContent />
      </div>

      {/* Mobile Sidebar - Full Screen Overlay with Glass Effect */}
      <Sheet open={open} onOpenChange={onOpenChange}>
        <SheetContent side="left" className="p-0 w-full sm:w-80 bg-transparent border-r-0">
          <SidebarContent />
        </SheetContent>
      </Sheet>
    </>
  )
}