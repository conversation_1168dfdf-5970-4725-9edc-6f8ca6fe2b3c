"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useAuth } from "@/components/auth-provider"
import { useLanguage } from "@/components/language-provider"
import { Bot, MessageSquare, Mic, Globe } from "lucide-react"

export function LoginScreen() {
  const { signInWithGoogle } = useAuth()
  const { t } = useLanguage()

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-3 rounded-full">
              <Bot className="h-8 w-8 text-white" />
            </div>
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            ChatNova
          </h1>
          <p className="mt-2 text-muted-foreground">{t("aiPoweredChatbot")}</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>{t("getStarted")}</CardTitle>
            <CardDescription>{t("signInToStart")}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="flex items-center space-x-3 p-3 bg-muted rounded-lg">
                <MessageSquare className="h-5 w-5 text-blue-600" />
                <span className="text-sm">{t("aiConversations")}</span>
              </div>
              <div className="flex items-center space-x-3 p-3 bg-muted rounded-lg">
                <Mic className="h-5 w-5 text-green-600" />
                <span className="text-sm">{t("voiceInput")}</span>
              </div>
              <div className="flex items-center space-x-3 p-3 bg-muted rounded-lg">
                <Globe className="h-5 w-5 text-purple-600" />
                <span className="text-sm">{t("multilingualSupport")}</span>
              </div>
            </div>

            <Button onClick={signInWithGoogle} className="w-full" size="lg">
              <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                />
                <path
                  fill="currentColor"
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                />
                <path
                  fill="currentColor"
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                />
                <path
                  fill="currentColor"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                />
              </svg>
              {t("signInWithGoogle")}
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
