import { MongoClient, ServerApiVersion } from 'mongodb';

// Connection URI (replace with your actual MongoDB Atlas connection string)
const uri = process.env.MONGODB_URI || "mongodb+srv://your-username:<EMAIL>/?retryWrites=true&w=majority";

// Create a MongoClient with a MongoClientOptions object to set the Stable API version
const client = new MongoClient(uri, {
  serverApi: {
    version: ServerApiVersion.v1,
    strict: true,
    deprecationErrors: true,
  }
});

let clientPromise: Promise<MongoClient>;

if (process.env.NODE_ENV === 'development') {
  // In development mode, use a global variable so that the value
  // is preserved across module reloads caused by HMR (Hot Module Replacement).
  let globalWithMongo = global as typeof globalThis & {
    _mongoClientPromise?: Promise<MongoClient>;
  };

  if (!globalWithMongo._mongoClientPromise) {
    globalWithMongo._mongoClientPromise = client.connect();
  }
  clientPromise = globalWithMongo._mongoClientPromise;
} else {
  // In production mode, it's best to not use a global variable.
  clientPromise = client.connect();
}

export default clientPromise;

// Helper function to get a database instance
export async function getDatabase(dbName = 'chatnova') {
  const client = await clientPromise;
  return client.db(dbName);
}

// Helper function to get a collection
export async function getCollection(collectionName: string, dbName = 'chatnova') {
  const db = await getDatabase(dbName);
  return db.collection(collectionName);
}

// Helper function to close the connection
export async function closeConnection() {
  await client.close();
}