// MongoDB Playground for ChatNova Backend
// Use this script in MongoDB Atlas > Clusters > Sandbox > Collections > "Playground"

// 1. Switch to our database
use("chatnova")

// 2. Create 'users' collection and insert a sample user
db.users.insertOne({
  _id: ObjectId(),
  name: "Alice Example",
  email: "<EMAIL>",
  passwordHash: "<bcrypt-hash>", // store hashed passwords
  role: "user",
  image: "https://api.dicebear.com/7.x/avataaars/svg?seed=<EMAIL>",
  createdAt: new Date(),
  lastLogin: new Date()
})

// 3. Create 'messages' collection and insert sample chat messages
db.messages.insertMany([
  {
    _id: ObjectId(),
    userId: db.users.findOne({ email: "<EMAIL>" })._id,
    role: "user",
    content: "Hello, ChatNova!",
    createdAt: new Date()
  },
  {
    _id: ObjectId(),
    userId: db.users.findOne({ email: "<EMAIL>" })._id,
    role: "assistant",
    content: "Hi Alice! How can I assist you today?",
    createdAt: new Date()
  }
])

// 4. Create 'documents' collection for uploaded files
//    Insert a sample PDF metadata record
db.documents.insertOne({
  _id: ObjectId(),
  userId: db.users.findOne({ email: "<EMAIL>" })._id,
  filename: "sample.pdf",
  contentType: "application/pdf",
  size: 102400, // bytes
  uploadedAt: new Date(),
  s3Url: "https://your-s3-bucket/sample.pdf" // or your storage URL
})

// 5. Indexes for performance
//    Ensure email is unique
db.users.createIndex({ email: 1 }, { unique: true })
//    Index messages by user and createdAt for fast queries
db.messages.createIndex({ userId: 1, createdAt: -1 })
//    Index documents by user
 db.documents.createIndex({ userId: 1 })

// 6. Common queries
//    Find a user by email
const alice = db.users.findOne({ email: "<EMAIL>" })
print(`Found user: ${alice.name} (${alice._id})`)

//    Fetch last 10 messages for a user
const recentMessages = db.messages.find({ userId: alice._id })
  .sort({ createdAt: -1 })
  .limit(10)
  .toArray()
printjson(recentMessages)

//    Update user's lastLogin
db.users.updateOne(
  { _id: alice._id },
  { $set: { lastLogin: new Date() } }
)

//    Delete a document record
db.documents.deleteOne({ filename: "sample.pdf" })

// End of Playground Script
