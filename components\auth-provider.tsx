"use client"

import { createContext, useContext, useState, useEffect, ReactNode } from "react"
import { useRout<PERSON>, usePathname } from "next/navigation"

interface User {
  id: string
  name: string
  email: string
  image?: string
  role?: "user" | "admin"
  createdAt?: string
  lastLogin?: string
}

interface AuthToken {
  token: string
  expiresAt: number
}

interface AuthError {
  message: string
  code?: string
  field?: string
}

interface AuthState {
  status: "idle" | "loading" | "authenticated" | "unauthenticated" | "error"
  user: User | null
  error: AuthError | null
  token: AuthToken | null
}

interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  authStatus: AuthState["status"]
  error: AuthError | null
  login: (email: string, password: string) => Promise<void>
  register: (name: string, email: string, password: string) => Promise<void>
  logout: () => void
  resetPassword: (email: string) => Promise<void>
  updateProfile: (userData: Partial<User>) => Promise<void>
  clearError: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Helper functions for token management
const storeToken = (token: string, expiresIn: number) => {
  const expiresAt = Date.now() + expiresIn * 1000
  const tokenData: AuthToken = { token, expiresAt }
  localStorage.setItem("chatnova_token", JSON.stringify(tokenData))
  return tokenData
}

const getStoredToken = (): AuthToken | null => {
  try {
    const tokenData = localStorage.getItem("chatnova_token")
    if (!tokenData) return null
    
    const parsedToken = JSON.parse(tokenData) as AuthToken
    
    // Check if token is expired
    if (parsedToken.expiresAt < Date.now()) {
      localStorage.removeItem("chatnova_token")
      return null
    }
    
    return parsedToken
  } catch (error) {
    console.error("Error retrieving token:", error)
    return null
  }
}

export function AuthProvider({ children }: { children: ReactNode }) {
  const [authState, setAuthState] = useState<AuthState>({
    status: "idle",
    user: null,
    error: null,
    token: null
  })
  const router = useRouter()
  const pathname = usePathname()

  const setUser = (user: User | null) => {
    setAuthState(prev => ({ ...prev, user }))
  }

  const setError = (error: AuthError | null) => {
    setAuthState(prev => ({ ...prev, error, status: error ? "error" : prev.status }))
  }

  const setStatus = (status: AuthState["status"]) => {
    setAuthState(prev => ({ ...prev, status }))
  }

  const setToken = (token: AuthToken | null) => {
    setAuthState(prev => ({ ...prev, token }))
  }

  // Check if user is authenticated on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        setStatus("loading")
        
        // Check for token first
        const tokenData = getStoredToken()
        
        if (!tokenData) {
          setStatus("unauthenticated")
          return
        }
        
        // Token exists, now get user data
        const storedUser = localStorage.getItem("chatnova_user")
        
        if (storedUser) {
          const userData = JSON.parse(storedUser) as User
          setUser(userData)
          setToken(tokenData)
          setStatus("authenticated")
        } else {
          // We have a token but no user data - this shouldn't happen
          // In a real app, we would validate the token with the server here
          localStorage.removeItem("chatnova_token")
          setStatus("unauthenticated")
        }
      } catch (error) {
        console.error("Authentication error:", error)
        setError({ message: "Failed to authenticate. Please log in again." })
        setStatus("unauthenticated")
      }
    }

    checkAuth()
  }, [])

  // Redirect unauthenticated users away from protected routes
  useEffect(() => {
    if (authState.status !== "idle" && authState.status !== "loading") {
      const isProtectedRoute = pathname?.startsWith("/chat") || pathname?.startsWith("/settings")
      const isAuthRoute = pathname === "/login" || pathname === "/register" || pathname === "/reset-password"

      if (isProtectedRoute && authState.status !== "authenticated") {
        router.push("/login")
      } else if (isAuthRoute && authState.status === "authenticated") {
        router.push("/chat")
      }
    }
  }, [authState.status, pathname, router])

  // Set up token refresh interval
  useEffect(() => {
    if (!authState.token) return

    // Calculate time until token expiration (minus a buffer of 5 minutes)
    const timeUntilExpiry = authState.token.expiresAt - Date.now() - (5 * 60 * 1000)
    
    if (timeUntilExpiry <= 0) {
      // Token is already expired or about to expire
      logout()
      return
    }

    // Set up refresh timer
    const refreshTimer = setTimeout(() => {
      // In a real app, this would call an API endpoint to refresh the token
      // For now, we'll just extend the current token
      if (authState.user) {
        const newToken = storeToken(authState.token.token, 60 * 60) // Extend for another hour
        setToken(newToken)
      }
    }, timeUntilExpiry)

    return () => clearTimeout(refreshTimer)
  }, [authState.token])

  // Login function
  const login = async (email: string, password: string) => {
    try {
      setStatus("loading")
      setError(null)
      
      // Validate inputs
      if (!email || !email.includes('@')) {
        setError({ message: "Please enter a valid email address", field: "email" })
        setStatus("unauthenticated")
        return
      }
      
      if (!password || password.length < 6) {
        setError({ message: "Password must be at least 6 characters", field: "password" })
        setStatus("unauthenticated")
        return
      }

      // In a real app, this would be an API call to your backend
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 800))
      
      // For demo purposes, we'll simulate a successful login
      // In a real app, the server would return a JWT token
      const mockToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
      const tokenExpiry = 60 * 60 // 1 hour in seconds
      
      const mockUser: User = {
        id: "user_" + Math.random().toString(36).substring(2, 9),
        name: email.split("@")[0],
        email: email,
        image: `https://api.dicebear.com/7.x/avataaars/svg?seed=${email}`,
        role: "user",
        createdAt: new Date().toISOString(),
        lastLogin: new Date().toISOString()
      }
      
      // Store token and user in local storage
      const tokenData = storeToken(mockToken, tokenExpiry)
      localStorage.setItem("chatnova_user", JSON.stringify(mockUser))
      
      // Update state
      setUser(mockUser)
      setToken(tokenData)
      setStatus("authenticated")
      
      router.push("/chat")
    } catch (error) {
      console.error("Login error:", error)
      setError({ 
        message: "Login failed. Please check your credentials.",
        code: "auth/invalid-credentials"
      })
      setStatus("unauthenticated")
    }
  }

  // Register function
  const register = async (name: string, email: string, password: string) => {
    try {
      setStatus("loading")
      setError(null)
      
      // Validate inputs
      if (!name || name.length < 2) {
        setError({ message: "Name must be at least 2 characters", field: "name" })
        setStatus("unauthenticated")
        return
      }
      
      if (!email || !email.includes('@')) {
        setError({ message: "Please enter a valid email address", field: "email" })
        setStatus("unauthenticated")
        return
      }
      
      if (!password || password.length < 6) {
        setError({ message: "Password must be at least 6 characters", field: "password" })
        setStatus("unauthenticated")
        return
      }

      // In a real app, this would be an API call to your backend
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // For demo purposes, we'll simulate a successful registration
      const mockToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
      const tokenExpiry = 60 * 60 // 1 hour in seconds
      
      const mockUser: User = {
        id: "user_" + Math.random().toString(36).substring(2, 9),
        name: name,
        email: email,
        image: `https://api.dicebear.com/7.x/avataaars/svg?seed=${email}`,
        role: "user",
        createdAt: new Date().toISOString(),
        lastLogin: new Date().toISOString()
      }
      
      // Store token and user in local storage
      const tokenData = storeToken(mockToken, tokenExpiry)
      localStorage.setItem("chatnova_user", JSON.stringify(mockUser))
      
      // Update state
      setUser(mockUser)
      setToken(tokenData)
      setStatus("authenticated")
      
      router.push("/chat")
    } catch (error) {
      console.error("Registration error:", error)
      setError({ 
        message: "Registration failed. The email may already be in use.",
        code: "auth/email-already-in-use"
      })
      setStatus("unauthenticated")
    }
  }

  // Reset password function
  const resetPassword = async (email: string) => {
    try {
      setStatus("loading")
      setError(null)
      
      if (!email || !email.includes('@')) {
        setError({ message: "Please enter a valid email address", field: "email" })
        setStatus("unauthenticated")
        return
      }
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800))
      
      // In a real app, this would send a password reset email
      setStatus("unauthenticated")
      
      // Return to login with success message
      router.push("/login?reset=success")
    } catch (error) {
      console.error("Password reset error:", error)
      setError({ 
        message: "Failed to send password reset email. Please try again.",
        code: "auth/reset-failed"
      })
      setStatus("unauthenticated")
    }
  }

  // Update profile function
  const updateProfile = async (userData: Partial<User>) => {
    try {
      setStatus("loading")
      setError(null)
      
      if (!authState.user) {
        setError({ message: "You must be logged in to update your profile" })
        return
      }
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800))
      
      // Update user data
      const updatedUser = { ...authState.user, ...userData }
      localStorage.setItem("chatnova_user", JSON.stringify(updatedUser))
      setUser(updatedUser)
      setStatus("authenticated")
    } catch (error) {
      console.error("Profile update error:", error)
      setError({ 
        message: "Failed to update profile. Please try again.",
        code: "auth/update-failed"
      })
    }
  }

  // Logout function
  const logout = () => {
    localStorage.removeItem("chatnova_user")
    localStorage.removeItem("chatnova_token")
    setUser(null)
    setToken(null)
    setStatus("unauthenticated")
    router.push("/login")
  }

  // Clear error function
  const clearError = () => {
    setError(null)
  }

  return (
    <AuthContext.Provider
      value={{
        user: authState.user,
        isLoading: authState.status === "loading" || authState.status === "idle",
        isAuthenticated: authState.status === "authenticated",
        authStatus: authState.status,
        error: authState.error,
        login,
        register,
        logout,
        resetPassword,
        updateProfile,
        clearError
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}