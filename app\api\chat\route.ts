import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize Google Gemini API
const googleAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY || 'your-api-key');

export async function POST(request: NextRequest) {
  try {
    const { messages, uploadedFiles, model = 'gemini' } = await request.json();

    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json(
        { success: false, error: 'Invalid messages format' },
        { status: 400 }
      );
    }

    // Format messages for the AI model
    const formattedMessages = messages.map(msg => ({
      role: msg.role,
      parts: [{ text: msg.content }],
    }));

    // Add context from uploaded files if available
    let systemPrompt = "You are <PERSON><PERSON><PERSON><PERSON>, a helpful AI assistant. ";
    
    if (uploadedFiles && uploadedFiles.length > 0) {
      systemPrompt += "I've uploaded the following documents that you can reference in your responses:\n\n";
      
      for (const file of uploadedFiles) {
        systemPrompt += `Document: ${file.name} (${file.type})\nContent:\n${file.content}\n\n`;
      }
      
      systemPrompt += "Please use this information to provide accurate and helpful responses.";
    }

    // Add system message at the beginning
    formattedMessages.unshift({
      role: 'system',
      parts: [{ text: systemPrompt }],
    });

    let response;
    
    if (model === 'gemini') {
      // Use Google Gemini model
      const geminiModel = googleAI.getGenerativeModel({ model: 'gemini-pro' });
      
      const result = await geminiModel.generateContent({
        contents: formattedMessages,
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 4096,
        },
      });
      
      const text = result.response.text();
      response = { content: text };
    } else {
      // Simulate Grok model (in a real app, you would use the actual Grok API)
      // This is just a placeholder for demonstration
      const userMessage = messages[messages.length - 1].content;
      
      // Simulate a delay to make it feel more realistic
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      response = { 
        content: `[Grok Model Simulation] Response to: "${userMessage.substring(0, 50)}${userMessage.length > 50 ? '...' : ''}"\n\nI'm simulating a response from the Grok model. In a production environment, this would be connected to the actual Grok API. For now, I'll provide a helpful response based on your query.\n\nHow can I assist you further?` 
      };
    }

    return NextResponse.json({
      success: true,
      ...response,
      model,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Chat API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      },
      { status: 500 }
    );
  }
}