# 🚀 Chat<PERSON>ova - Next-Generation AI Assistant

<div align="center">

![<PERSON><PERSON><PERSON><PERSON> Logo](https://img.shields.io/badge/ChatNova-AI%20Assistant-blue?style=for-the-badge&logo=openai)
[![Next.js](https://img.shields.io/badge/Next.js-15.2.4-black?style=for-the-badge&logo=next.js)](https://nextjs.org/)
[![React](https://img.shields.io/badge/React-19-blue?style=for-the-badge&logo=react)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5-blue?style=for-the-badge&logo=typescript)](https://www.typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-3.4.17-38B2AC?style=for-the-badge&logo=tailwind-css)](https://tailwindcss.com/)

**A stunning, feature-rich AI chat application with advanced PDF processing, voice interaction, and beautiful UI**

[🌟 Live Demo](#) • [📖 Documentation](#) • [🐛 Report Bug](#) • [💡 Request Feature](#)

</div>

---

## ✨ Features

### 🎨 **Beautiful Interface**
- 🌟 **Modern UI**: Stunning, responsive interface inspired by ChatGPT
- 🎭 **Dark/Light Themes**: Seamless theme switching with system preference detection
- 📱 **Fully Responsive**: Perfect experience on desktop, tablet, and mobile devices
- 🎯 **Full-Screen Mode**: Immersive chat experience with distraction-free interface

### 🤖 **AI-Powered Conversations**
- 🧠 **Google Gemini Integration**: Advanced AI conversations with Gemini Pro
- 💬 **Real-time Chat**: Instant responses with typing indicators
- 🔄 **Context Awareness**: Maintains conversation context for natural interactions
- 📝 **Message History**: Persistent chat history with local storage

### 📄 **Advanced PDF Processing**
- 📋 **Smart Upload**: Drag-and-drop PDF upload with visual feedback
- 🔍 **Text Extraction**: Advanced PDF text extraction with multiple fallback methods
- 🖼️ **OCR Technology**: Tesseract.js integration for image-based PDFs
- 📊 **Document Analysis**: AI-powered analysis of uploaded documents

### 🎤 **Voice & Audio Features**
- 🗣️ **Voice Input**: Web Speech API integration for hands-free interaction
- 🔊 **Text-to-Speech**: High-quality audio playback of AI responses
- 🎵 **Audio Controls**: Play, pause, and volume control for TTS

### 🔐 **Authentication & Security**
- 👤 **User Accounts**: Secure registration and login system
- 🔒 **Local Storage Auth**: Client-side authentication with JWT-like tokens
- 🛡️ **Form Validation**: Comprehensive input validation and error handling
- 🔄 **Password Reset**: Secure password recovery workflow

## 🛠️ Tech Stack

### **Frontend Excellence**
- ⚡ **Next.js 15.2.4**: Latest App Router with server components
- ⚛️ **React 19**: Modern React with concurrent features
- 🔷 **TypeScript 5**: Full type safety and developer experience
- 🎨 **Tailwind CSS 3.4.17**: Utility-first styling with custom design system

### **UI Components & Design**
- 🎯 **Radix UI**: Accessible, unstyled component primitives
- 🎭 **Framer Motion**: Smooth animations and transitions
- 🎨 **Lucide Icons**: Beautiful, consistent iconography
- 📱 **Responsive Design**: Mobile-first approach with breakpoint system

### **AI & Processing**
- 🤖 **Google Gemini API**: State-of-the-art language model integration
- 📄 **PDF.js**: Client-side PDF rendering and text extraction
- 🔍 **Tesseract.js**: OCR for image-based document processing
- 🎤 **Web Speech API**: Native browser speech recognition and synthesis

## 🚀 Getting Started

### 📋 Prerequisites

- **Node.js 18+** and npm/yarn/pnpm
- **Google AI API key** (for Gemini integration)
- Modern web browser with JavaScript enabled

### ⚡ Quick Start

1. **Clone the repository:**
   ```bash
   git clone https://github.com/Sagexd08/ChatNova.git
   cd ChatNova
   ```

2. **Install dependencies:**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Set up environment variables:**

   Create a `.env.local` file in the root directory:
   ```env
   # Google AI API Key (Required)
   GOOGLE_API_KEY=your_google_ai_api_key_here

   # Optional: Custom API endpoints
   NEXT_PUBLIC_API_URL=http://localhost:3000
   ```

   > 🔑 **Get your Google AI API key:**
   > 1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   > 2. Create a new API key
   > 3. Copy and paste it into your `.env.local` file

4. **Start the development server:**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

5. **Open your browser:**

   Navigate to [http://localhost:3000](http://localhost:3000) and start chatting! 🎉

## 📖 Usage Guide

### 🎯 **Getting Started**
1. **🔐 Authentication**: Create an account or log in to access the full chat interface
2. **💬 Start Chatting**: Begin a conversation with the AI assistant
3. **📄 Upload Documents**: Drag and drop PDF files for analysis and discussion
4. **🎤 Voice Interaction**: Use the microphone for hands-free input
5. **🔊 Listen to Responses**: Click the speaker icon to hear AI responses

### 🎨 **Interface Features**
- **🌙 Theme Toggle**: Switch between light and dark modes
- **📱 Responsive Design**: Seamless experience across all devices
- **🎯 Full-Screen Mode**: Distraction-free chat experience
- **📝 Message History**: All conversations are saved locally

### 📄 **PDF Processing**
- **📋 Smart Upload**: Drag-and-drop or click to upload PDF files
- **🔍 Text Extraction**: Automatic text extraction with OCR fallback
- **💬 Document Chat**: Ask questions about your uploaded documents
- **📊 Analysis**: Get insights and summaries from your PDFs

## 📁 Project Structure

```
ChatNova/
├── 📁 app/                    # Next.js App Router
│   ├── 📁 api/               # API routes
│   │   ├── 📁 chat/          # Chat API endpoint
│   │   └── 📁 upload-pdf/    # PDF upload endpoint
│   ├── 📁 chat/              # Chat page
│   ├── 📁 login/             # Authentication pages
│   ├── 📁 register/
│   └── 📄 layout.tsx         # Root layout
├── 📁 components/            # React components
│   ├── 📁 ui/               # Reusable UI components
│   ├── 📄 chat-interface.tsx # Main chat interface
│   ├── 📄 auth-provider.tsx  # Authentication context
│   └── 📄 theme-provider.tsx # Theme management
├── 📁 lib/                   # Utility functions
│   ├── 📄 utils.ts          # Helper functions
│   └── 📄 firebase.ts       # Firebase configuration
├── 📁 hooks/                 # Custom React hooks
├── 📁 public/               # Static assets
└── 📁 styles/               # Global styles
```

## 🚀 Deployment

### **Vercel (Recommended)**
1. Push your code to GitHub
2. Connect your repository to [Vercel](https://vercel.com)
3. Add your environment variables in Vercel dashboard
4. Deploy automatically on every push

### **Other Platforms**
- **Netlify**: Works with static export
- **Railway**: Full-stack deployment
- **Heroku**: Container deployment

## 🔒 Security & Privacy

### **🛡️ Security Features**
- 🔐 **Secure Authentication**: Client-side token management
- 🛡️ **Input Validation**: Comprehensive form validation
- 🔒 **XSS Protection**: Sanitized user inputs
- 📝 **Type Safety**: Full TypeScript coverage

### **🔐 Privacy**
- 📱 **Local Storage**: All data stored locally in your browser
- 🚫 **No Database**: No personal data stored on servers
- 🔒 **Secure API**: Direct communication with Google Gemini API
- 🛡️ **No Tracking**: Privacy-focused design

## 📄 License

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- 🤖 **Google Gemini API** for advanced AI capabilities
- ⚡ **Next.js Team** for the incredible framework
- 🎨 **Tailwind CSS** for beautiful styling
- 📄 **PDF.js & Tesseract.js** for document processing
- 🎯 **Radix UI** for accessible components
- 🎭 **Lucide React** for beautiful icons

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📞 Support

- 📧 **Email**: [<EMAIL>](mailto:<EMAIL>)
- 🐛 **Issues**: [GitHub Issues](https://github.com/Sagexd08/ChatNova/issues)
- 💬 **Discussions**: [GitHub Discussions](https://github.com/Sagexd08/ChatNova/discussions)

---

<div align="center">

**Made with ❤️ by the ChatNova Team**

⭐ **Star this repo if you find it helpful!** ⭐

</div>