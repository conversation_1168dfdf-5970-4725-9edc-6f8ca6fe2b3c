# ChatNova - AI Assistant

<PERSON><PERSON><PERSON><PERSON> is a modern AI chat application with a ChatGPT-like interface, featuring PDF document analysis, voice interaction, and authentication.

## Features

- **Modern UI**: Clean, responsive interface inspired by ChatGPT
- **Authentication**: User accounts with secure login/registration
- **PDF Analysis**: Upload and analyze PDF documents
- **Voice Interaction**: Voice input and text-to-speech capabilities
- **Multiple AI Models**: Switch between Gemini and Grok models
- **Responsive Design**: Works on desktop, tablet, and mobile devices

## Tech Stack

- **Frontend**: Next.js, React, TypeScript, Tailwind CSS
- **Authentication**: NextAuth.js
- **Database**: MongoDB Atlas
- **AI Integration**: Google Gemini API
- **PDF Processing**: pdf.js, Tesseract.js (OCR)
- **Voice**: Web Speech API

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- MongoDB Atlas account (for database)
- Google AI API key (for Gemini)

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/chatnova.git
   cd chatnova
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env.local` file in the root directory with the following variables:
   ```
   # MongoDB
-  MONGODB_URI=your_mongodb_atlas_connection_string
+  MONGODB_URI="mongodb+srv://sohomchatterjee07:<db_password>@chatnova.mmo3pan.mongodb.net/?retryWrites=true&w=majority&appName=ChatNova"
+  
+  # Google AI
   GOOGLE_API_KEY=your_google_ai_api_key
   
   # NextAuth
   NEXTAUTH_SECRET=your_random_secret_key
   NEXTAUTH_URL=http://localhost:3000
   ```

4. Run the development server:
   ```bash
   npm run dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Usage

1. **Authentication**: Register or log in to access the chat interface
2. **Chat**: Start a new conversation and chat with the AI
3. **Document Analysis**: Upload PDFs to analyze their content
4. **Voice Input**: Click the microphone icon to use voice input
5. **Text-to-Speech**: Click the speaker icon on AI responses to hear them read aloud
6. **Model Selection**: Switch between Gemini and Grok models in the header

## Project Structure

- `/app`: Next.js app router pages and API routes
- `/components`: React components
- `/lib`: Utility functions and shared code
- `/public`: Static assets
- `/styles`: Global CSS styles

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Google Gemini API for AI capabilities
- Next.js team for the amazing framework
- Tailwind CSS for styling
- PDF.js and Tesseract.js for document processing

## 🔒 Security Features

- 🔐 Bcrypt password hashing
- 🔑 JWT authentication
- 🛡️ Rate limiting
- 🔒 XSS protection
- 🚫 CSRF prevention
- 📝 Input validation
- 🔍 SQL injection prevention