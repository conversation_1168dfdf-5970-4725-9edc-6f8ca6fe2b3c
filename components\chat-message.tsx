"use client"

import { useState } from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { <PERSON>ltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Co<PERSON>, Check, ThumbsUp, ThumbsDown, Volume2, <PERSON>rkles, User } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { cn } from "@/lib/utils"

interface Message {
  id: string
  role: "user" | "assistant" | "system"
  content: string
  createdAt: Date
}

interface ChatMessageProps {
  message: Message
  isLoading?: boolean
}

export function ChatMessage({ message, isLoading = false }: ChatMessageProps) {
  const { toast } = useToast()
  const [copied, setCopied] = useState(false)
  const [liked, setLiked] = useState(false)
  const [disliked, setDisliked] = useState(false)
  const [isSpeaking, setIsSpeaking] = useState(false)
  
  const isUser = message.role === "user"
  const isAssistant = message.role === "assistant"
  
  const copyToClipboard = () => {
    navigator.clipboard.writeText(message.content)
    setCopied(true)
    toast({
      title: "Copied to clipboard",
      description: "Message content has been copied to your clipboard",
    })
    setTimeout(() => setCopied(false), 2000)
  }
  
  const handleLike = () => {
    setLiked(!liked)
    setDisliked(false)
  }
  
  const handleDislike = () => {
    setDisliked(!disliked)
    setLiked(false)
  }
  
  const speakMessage = () => {
    if ('speechSynthesis' in window) {
      if (isSpeaking) {
        window.speechSynthesis.cancel()
        setIsSpeaking(false)
        return
      }
      
      const utterance = new SpeechSynthesisUtterance(message.content)
      utterance.rate = 1.0
      utterance.pitch = 1.0
      utterance.volume = 1.0
      
      utterance.onend = () => {
        setIsSpeaking(false)
      }
      
      utterance.onerror = () => {
        setIsSpeaking(false)
        toast({
          title: "Speech error",
          description: "An error occurred while speaking the message",
          variant: "destructive",
        })
      }
      
      window.speechSynthesis.speak(utterance)
      setIsSpeaking(true)
    } else {
      toast({
        title: "Speech not supported",
        description: "Your browser doesn't support text-to-speech",
        variant: "destructive",
      })
    }
  }
  
  // Format timestamp
  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    }).format(date)
  }

  return (
    <div className={cn(
      "px-4 py-6 sm:px-6",
      isUser ? "bg-white dark:bg-gray-900" : "bg-gray-50 dark:bg-gray-800"
    )}>
      <div className="max-w-3xl mx-auto">
        <div className="flex items-start gap-4 sm:gap-6">
          {/* Avatar */}
          <div className="mt-1">
            {isUser ? (
              <Avatar className="h-8 w-8 border border-gray-200 dark:border-gray-700">
                <AvatarImage src="/placeholder-user.jpg" alt="User" />
                <AvatarFallback>
                  <User className="h-4 w-4" />
                </AvatarFallback>
              </Avatar>
            ) : (
              <Avatar className="h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-500 border border-gray-200 dark:border-gray-700">
                <AvatarFallback>
                  <Sparkles className="h-4 w-4 text-white" />
                </AvatarFallback>
              </Avatar>
            )}
          </div>
          
          {/* Message content */}
          <div className="flex-1 space-y-2 overflow-hidden">
            {/* Message header */}
            <div className="flex items-center justify-between">
              <div className="font-medium text-sm text-gray-900 dark:text-gray-100">
                {isUser ? "You" : "ChatNova"}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {message.createdAt ? formatTime(message.createdAt) : ""}
              </div>
            </div>
            
            {/* Message body */}
            <div className="prose prose-sm dark:prose-invert max-w-none">
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: "0ms" }}></div>
                  <div className="h-2 w-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: "150ms" }}></div>
                  <div className="h-2 w-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: "300ms" }}></div>
                </div>
              ) : (
                <div className="whitespace-pre-wrap">{message.content}</div>
              )}
            </div>
            
            {/* Message actions */}
            {!isLoading && isAssistant && (
              <div className="flex items-center gap-2 mt-4">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 rounded-md text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                        onClick={copyToClipboard}
                      >
                        {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Copy to clipboard</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className={cn(
                          "h-8 w-8 rounded-md",
                          isSpeaking 
                            ? "text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20" 
                            : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                        )}
                        onClick={speakMessage}
                      >
                        <Volume2 className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{isSpeaking ? "Stop speaking" : "Speak message"}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <div className="flex items-center ml-auto">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className={cn(
                            "h-8 w-8 rounded-md",
                            liked 
                              ? "text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20" 
                              : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                          )}
                          onClick={handleLike}
                        >
                          <ThumbsUp className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Helpful response</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className={cn(
                            "h-8 w-8 rounded-md",
                            disliked 
                              ? "text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20" 
                              : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                          )}
                          onClick={handleDislike}
                        >
                          <ThumbsDown className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Unhelpful response</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}