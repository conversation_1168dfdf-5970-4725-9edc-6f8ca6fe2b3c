"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { useChat } from "ai/react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  Send,
  Bot,
  User,
  Sparkles,
  Volume2,
  VolumeX,
  Paperclip,
  FileText,
  ImageIcon,
  X,
  Download,
} from "lucide-react"
import { useLanguage } from "@/components/language-provider"
import { useChatHistory } from "@/hooks/use-chat-history"
import { useTextToSpeech } from "@/hooks/use-text-to-speech"
import { DocumentUpload } from "@/components/document-upload"
import { VoiceInput } from "@/components/voice-input"
import { cn } from "@/lib/utils"

interface ChatInterfaceProps {
  conversationId: string | null
}

interface UploadedFile {
  id: string
  name: string
  type: string
  size: number
  content: string
  url?: string
}

export function ChatInterface({ conversationId }: ChatInterfaceProps) {
  const { t } = useLanguage()
  const { saveMessage, getConversationMessages } = useChatHistory()
  const [initialMessages, setInitialMessages] = useState<any[]>([])
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([])
  const [showDocumentUpload, setShowDocumentUpload] = useState(false)

  const { messages, input, handleInputChange, handleSubmit, isLoading, setInput } = useChat({
    api: "/api/chat",
    initialMessages,
    body: {
      files: uploadedFiles,
    },
    onFinish: (message) => {
      if (conversationId) {
        saveMessage(conversationId, message)
      }
    },
  })

  // Handle voice commands
  const handleVoiceCommand = (command: string, args?: string) => {
    switch (command) {
      case 'upload':
        setShowDocumentUpload(true)
        break
      case 'clear':
        // Clear the input field
        setInput('')
        break
      case 'send':
        // Submit the form if there's input
        if (input.trim() || uploadedFiles.length > 0) {
          const event = new Event('submit', { bubbles: true, cancelable: true })
          document.querySelector('form')?.dispatchEvent(event)
        }
        break
      default:
        break
    }
  }
  const { speak, stop, isSpeaking, isSupported: ttsSupported } = useTextToSpeech()

  const scrollAreaRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (conversationId) {
      const savedMessages = getConversationMessages(conversationId)
      setInitialMessages(savedMessages)
    }
  }, [conversationId, getConversationMessages])

  
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight
    }
  }, [messages])

  const onSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim() && uploadedFiles.length === 0) return

    const userMessage = {
      id: Date.now().toString(),
      role: "user" as const,
      content: input,
      files: uploadedFiles,
    }

    if (conversationId) {
      saveMessage(conversationId, userMessage)
    }

    handleSubmit(e)
    setUploadedFiles([])
  }

  const handleFileUpload = (files: UploadedFile[]) => {
    setUploadedFiles((prev) => [...prev, ...files])
    setShowDocumentUpload(false)
  }

  const removeFile = (fileId: string) => {
    setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId))
  }

  const handleSpeak = (text: string) => {
    if (isSpeaking) {
      stop()
    } else {
      speak(text)
    }
  }

  return (
    <div className="flex-1 flex flex-col relative bg-white dark:bg-gray-900">
      <ScrollArea className="flex-1 relative" ref={scrollAreaRef}>
        <div className="max-w-3xl mx-auto px-4">
          {messages.length === 0 && (
            <div className="flex flex-col items-center justify-center min-h-[60vh] text-center">
              <div className="mb-8">
                <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mb-4 mx-auto">
                  <Bot className="h-6 w-6 text-white" />
                </div>
                <h1 className="text-3xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  How can I help you today?
                </h1>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 w-full max-w-2xl">
                <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
                    📝 Help me write
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    Create content, emails, or documents
                  </div>
                </div>

                <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
                    📄 Analyze documents
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    Upload and discuss PDF files
                  </div>
                </div>

                <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
                    💡 Get answers
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    Ask questions on any topic
                  </div>
                </div>

                <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
                    🎤 Voice chat
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    Talk naturally with voice input
                  </div>
                </div>
              </div>
            </div>
          )}

          {messages.map((message, index) => (
            <div
              key={message.id}
              className={cn(
                "py-6 border-b border-gray-100 dark:border-gray-800 last:border-b-0",
                message.role === "assistant" ? "bg-gray-50 dark:bg-gray-800/50" : ""
              )}
            >
              <div className="max-w-3xl mx-auto px-4">
                <div className="flex gap-4">
                  <div className="flex-shrink-0">
                    {message.role === "assistant" ? (
                      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <Bot className="h-4 w-4 text-white" />
                      </div>
                    ) : (
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <User className="h-4 w-4 text-white" />
                      </div>
                    )}
                  </div>

                  <div className="flex-1 space-y-2">
                    {/* Files attached to message */}
                    {message.files && message.files.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-3">
                        {message.files.map((file: UploadedFile) => (
                          <div
                            key={file.id}
                            className="bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-2 flex items-center gap-2 text-xs"
                          >
                            {file.type.startsWith("image/") ? (
                              <ImageIcon className="h-4 w-4 text-blue-500" />
                            ) : (
                              <FileText className="h-4 w-4 text-green-500" />
                            )}
                            <span className="truncate max-w-[100px]">{file.name}</span>
                            {file.url && (
                              <Button variant="ghost" size="sm" className="h-4 w-4 p-0" asChild>
                                <a href={file.url} download={file.name}>
                                  <Download className="h-3 w-3" />
                                </a>
                              </Button>
                            )}
                          </div>
                        ))}
                      </div>
                    )}

                    <div className="prose prose-sm max-w-none dark:prose-invert">
                      <p className="text-gray-900 dark:text-gray-100 leading-relaxed whitespace-pre-wrap m-0">
                        {message.content}
                      </p>
                    </div>

                    <div className="flex items-center justify-between mt-3">
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                      </div>
                      {message.role === "assistant" && ttsSupported && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                          onClick={() => handleSpeak(message.content)}
                        >
                          {isSpeaking ? <VolumeX className="h-3 w-3" /> : <Volume2 className="h-3 w-3" />}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}

          {isLoading && (
            <div className="py-6 border-b border-gray-100 dark:border-gray-800 bg-gray-50 dark:bg-gray-800/50">
              <div className="max-w-3xl mx-auto px-4">
                <div className="flex gap-4">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                      <Bot className="h-4 w-4 text-white" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div
                        className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                        style={{ animationDelay: "0.1s" }}
                      ></div>
                      <div
                        className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                        style={{ animationDelay: "0.2s" }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>

      <div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
        <div className="max-w-3xl mx-auto p-4">
          {/* Uploaded Files Preview */}
          {uploadedFiles.length > 0 && (
            <div className="mb-4">
              <div className="flex flex-wrap gap-2">
                {uploadedFiles.map((file) => (
                  <div
                    key={file.id}
                    className="bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-2 flex items-center gap-2 text-sm"
                  >
                    {file.type.startsWith("image/") ? (
                      <ImageIcon className="h-4 w-4 text-blue-500" />
                    ) : (
                      <FileText className="h-4 w-4 text-green-500" />
                    )}
                    <span className="truncate max-w-[150px]">{file.name}</span>
                    <Button variant="ghost" size="sm" className="h-4 w-4 p-0" onClick={() => removeFile(file.id)}>
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          <form onSubmit={onSubmit}>
            <div className="relative flex items-end gap-2">
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-10 w-10 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
                onClick={() => setShowDocumentUpload(true)}
              >
                <Paperclip className="h-5 w-5" />
              </Button>

              <div className="flex-1 relative">
                <Input
                  value={input}
                  onChange={handleInputChange}
                  placeholder={uploadedFiles.length > 0 ? "Ask about your documents..." : "Message ChatNova..."}
                  disabled={isLoading}
                  className="min-h-[44px] pr-20 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl focus:border-gray-400 dark:focus:border-gray-500 focus:ring-0 resize-none"
                />
                <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1">
                  <VoiceInput
                    onTranscript={(text) => handleInputChange({ target: { value: text } } as any)}
                    onCommand={handleVoiceCommand}
                    className="h-8 w-8 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  />
                  <Button
                    type="submit"
                    disabled={isLoading || (!input.trim() && uploadedFiles.length === 0)}
                    size="sm"
                    className="h-8 w-8 p-0 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 hover:bg-gray-700 dark:hover:bg-gray-300 disabled:opacity-50 disabled:bg-gray-300 dark:disabled:bg-gray-600"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>

      {/* Document Upload Modal */}
      <DocumentUpload
        open={showDocumentUpload}
        onOpenChange={setShowDocumentUpload}
        onFilesUploaded={handleFileUpload}
      />
    </div>
  )
}
