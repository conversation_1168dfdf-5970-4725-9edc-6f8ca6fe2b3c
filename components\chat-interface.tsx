"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  Send,
  Bot,
  User,
  Sparkles,
  Volume2,
  VolumeX,
  Paperclip,
  FileText,
  ImageIcon,
  X,
  Download,
  Mic,
  MicOff,
  ChevronDown,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useLanguage } from "@/components/language-provider"
import { useChatHistory } from "@/hooks/use-chat-history"
import { useTextToSpeech } from "@/hooks/use-text-to-speech"
import { DocumentUpload } from "@/components/document-upload"
import { VoiceInput } from "@/components/voice-input"
import { cn } from "@/lib/utils"

interface ChatInterfaceProps {
  conversationId: string | null
}

interface UploadedFile {
  id: string
  name: string
  type: string
  size: number
  content: string
  url?: string
}

export function ChatInterface({ conversationId }: ChatInterfaceProps) {
  const { t } = useLanguage()
  const { saveMessage, getConversationMessages } = useChatHistory()
  const [initialMessages, setInitialMessages] = useState<any[]>([])
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([])
  const [showDocumentUpload, setShowDocumentUpload] = useState(false)
  const [selectedModel, setSelectedModel] = useState<string>("gemini")

  const models = [
    { id: "gemini", name: "Gemini Pro", description: "Google's advanced AI model" },
    { id: "gemma-3", name: "Gemma 3", description: "Google's open-source model via OpenRouter" },
    { id: "deepseek", name: "DeepSeek Chat", description: "DeepSeek's reasoning model via OpenRouter" },
  ]

  const [messages, setMessages] = useState<any[]>([])
  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  // Custom chat handler since we're using JSON responses
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim() && uploadedFiles.length === 0) return

    const userMessage = {
      id: Date.now().toString(),
      role: "user",
      content: input,
      files: uploadedFiles,
    }

    setMessages(prev => [...prev, userMessage])
    setInput("")
    setIsLoading(true)

    try {
      const response = await fetch("/api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          messages: [...messages, userMessage],
          uploadedFiles: uploadedFiles,
          selectedModel: selectedModel,
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to get response")
      }

      const data = await response.json()

      if (data.error) {
        throw new Error(data.error)
      }

      const assistantMessage = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: data.content,
        model: data.model,
      }

      setMessages(prev => [...prev, assistantMessage])

      if (conversationId) {
        saveMessage(conversationId, userMessage)
        saveMessage(conversationId, assistantMessage)
      }
    } catch (error) {
      console.error("Chat error:", error)
      const errorMessage = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: `Sorry, I encountered an error: ${error instanceof Error ? error.message : "Unknown error"}`,
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
      setUploadedFiles([])
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value)
  }

  // Handle voice commands
  const handleVoiceCommand = (command: string, args?: string) => {
    switch (command) {
      case 'upload':
        setShowDocumentUpload(true)
        break
      case 'clear':
        // Clear the input field
        setInput('')
        break
      case 'send':
        // Submit the form if there's input
        if (input.trim() || uploadedFiles.length > 0) {
          const event = new Event('submit', { bubbles: true, cancelable: true })
          document.querySelector('form')?.dispatchEvent(event)
        }
        break
      default:
        break
    }
  }
  const { speak, stop, isSpeaking, isSupported: ttsSupported } = useTextToSpeech()

  const scrollAreaRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (conversationId) {
      const savedMessages = getConversationMessages(conversationId)
      setInitialMessages(savedMessages)
    }
  }, [conversationId, getConversationMessages])

  
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight
    }
  }, [messages])

  const onSubmit = handleSubmit

  const handleFileUpload = (files: UploadedFile[]) => {
    setUploadedFiles((prev) => [...prev, ...files])
    setShowDocumentUpload(false)
  }

  const removeFile = (fileId: string) => {
    setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId))
  }

  const handleSpeak = (text: string) => {
    if (isSpeaking) {
      stop()
    } else {
      speak(text)
    }
  }

  return (
    <div className="flex-1 flex flex-col relative bg-black text-white h-full overflow-hidden">
      <ScrollArea className="flex-1 relative h-full" ref={scrollAreaRef}>
        <div className="max-w-6xl mx-auto px-4 sm:px-8 lg:px-12">
          {messages.length === 0 && (
            <div className="flex flex-col items-center justify-center min-h-[85vh] text-center py-16 relative">
              {/* Enhanced Background Elements */}
              <div className="absolute inset-0 overflow-hidden pointer-events-none">
                <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
                <div className="absolute top-1/3 right-1/4 w-24 h-24 bg-purple-500/10 rounded-full blur-2xl animate-pulse delay-1000"></div>
                <div className="absolute bottom-1/3 left-1/3 w-20 h-20 bg-cyan-500/10 rounded-full blur-xl animate-pulse delay-2000"></div>
                <div className="absolute top-1/2 right-1/3 w-16 h-16 bg-pink-500/10 rounded-full blur-lg animate-pulse delay-3000"></div>
              </div>

              <div className="mb-12 relative z-10">
                <div className="w-24 h-24 bg-gradient-to-br from-white via-gray-100 to-gray-200 rounded-3xl flex items-center justify-center mb-8 mx-auto shadow-2xl shadow-white/20 backdrop-blur-md border border-white/20 hover:scale-110 transition-all duration-700 hover:rotate-3">
                  <Bot className="h-12 w-12 text-black" />
                </div>
                <h1 className="text-4xl sm:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent mb-6 leading-tight tracking-tight">
                  How can I help you today?
                </h1>
                <p className="text-gray-300 text-xl sm:text-2xl max-w-4xl mx-auto leading-relaxed font-light">
                  I'm your advanced AI assistant, powered by cutting-edge technology and ready to help with anything you need.
                </p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 w-full max-w-7xl relative z-10">
                <div className="group p-8 bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-xl border border-white/20 rounded-3xl hover:bg-gradient-to-br hover:from-blue-500/10 hover:to-purple-500/10 hover:border-blue-400/30 cursor-pointer transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/20 relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="relative z-10">
                    <div className="text-5xl mb-6 group-hover:scale-110 transition-transform duration-500">✍️</div>
                    <div className="text-xl font-bold text-white mb-3 group-hover:text-blue-300 transition-colors duration-300">
                      Help me write
                    </div>
                    <div className="text-gray-400 leading-relaxed group-hover:text-gray-300 transition-colors duration-300">
                      Create content, emails, or documents with AI assistance
                    </div>
                  </div>
                </div>

                <div className="group p-8 bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-xl border border-white/20 rounded-3xl hover:bg-gradient-to-br hover:from-green-500/10 hover:to-emerald-500/10 hover:border-green-400/30 cursor-pointer transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-green-500/20 relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="relative z-10">
                    <div className="text-5xl mb-6 group-hover:scale-110 transition-transform duration-500">📄</div>
                    <div className="text-xl font-bold text-white mb-3 group-hover:text-green-300 transition-colors duration-300">
                      Analyze documents
                    </div>
                    <div className="text-gray-400 leading-relaxed group-hover:text-gray-300 transition-colors duration-300">
                      Upload and discuss PDF files with intelligent analysis
                    </div>
                  </div>
                </div>

                <div className="group p-8 bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-xl border border-white/20 rounded-3xl hover:bg-gradient-to-br hover:from-purple-500/10 hover:to-pink-500/10 hover:border-purple-400/30 cursor-pointer transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/20 relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="relative z-10">
                    <div className="text-5xl mb-6 group-hover:scale-110 transition-transform duration-500">💡</div>
                    <div className="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors duration-300">
                      Get answers
                    </div>
                    <div className="text-gray-400 leading-relaxed group-hover:text-gray-300 transition-colors duration-300">
                      Ask questions on any topic and get detailed responses
                    </div>
                  </div>
                </div>

                <div className="group p-8 bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-xl border border-white/20 rounded-3xl hover:bg-gradient-to-br hover:from-orange-500/10 hover:to-red-500/10 hover:border-orange-400/30 cursor-pointer transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-orange-500/20 relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="relative z-10">
                    <div className="text-5xl mb-6 group-hover:scale-110 transition-transform duration-500">🎤</div>
                    <div className="text-xl font-bold text-white mb-3 group-hover:text-orange-300 transition-colors duration-300">
                      Voice chat
                    </div>
                    <div className="text-gray-400 leading-relaxed group-hover:text-gray-300 transition-colors duration-300">
                      Talk naturally with voice input and audio responses
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {messages.map((message, index) => (
            <div
              key={message.id}
              className={cn(
                "py-12 border-b border-white/10 last:border-b-0 animate-in slide-in-from-bottom-4 duration-700",
                message.role === "assistant" ? "bg-gradient-to-r from-white/5 via-white/10 to-white/5 backdrop-blur-xl" : ""
              )}
              style={{ animationDelay: `${index * 150}ms` }}
            >
              <div className="max-w-6xl mx-auto px-8 lg:px-12">
                <div className="flex gap-8 items-start">
                  <div className="flex-shrink-0">
                    {message.role === "assistant" ? (
                      <div className="w-16 h-16 bg-gradient-to-br from-white via-gray-100 to-gray-200 rounded-3xl flex items-center justify-center shadow-2xl shadow-white/20 backdrop-blur-xl border border-white/30 hover:scale-110 hover:rotate-3 transition-all duration-500">
                        <Bot className="h-8 w-8 text-black" />
                      </div>
                    ) : (
                      <div className="w-16 h-16 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-3xl flex items-center justify-center shadow-2xl shadow-blue-500/30 hover:scale-110 hover:rotate-3 transition-all duration-500">
                        <User className="h-8 w-8 text-white" />
                      </div>
                    )}
                  </div>

                  <div className="flex-1 space-y-6">
                    {/* Enhanced Files Display */}
                    {message.files && message.files.length > 0 && (
                      <div className="flex flex-wrap gap-4 mb-6">
                        {message.files.map((file: UploadedFile) => (
                          <div
                            key={file.id}
                            className="bg-gradient-to-r from-white/10 to-white/20 backdrop-blur-xl border border-white/30 rounded-2xl p-4 flex items-center gap-4 text-sm hover:bg-gradient-to-r hover:from-white/20 hover:to-white/30 transition-all duration-500 hover:scale-105 hover:shadow-lg"
                          >
                            {file.type.startsWith("image/") ? (
                              <ImageIcon className="h-6 w-6 text-blue-400" />
                            ) : (
                              <FileText className="h-6 w-6 text-green-400" />
                            )}
                            <span className="truncate max-w-[200px] text-white font-semibold">{file.name}</span>
                            {file.url && (
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-white/20 rounded-xl transition-all duration-300" asChild>
                                <a href={file.url} download={file.name}>
                                  <Download className="h-5 w-5" />
                                </a>
                              </Button>
                            )}
                          </div>
                        ))}
                      </div>
                    )}

                    <div className={cn(
                      "prose prose-xl max-w-none prose-invert p-8 rounded-3xl backdrop-blur-xl border transition-all duration-500",
                      message.role === "assistant"
                        ? "bg-gradient-to-br from-white/10 to-white/20 border-white/30 hover:from-white/15 hover:to-white/25"
                        : "bg-gradient-to-br from-blue-500/10 to-purple-500/10 border-blue-400/30 hover:from-blue-500/15 hover:to-purple-500/15"
                    )}>
                      <p className="text-white leading-relaxed whitespace-pre-wrap m-0 text-lg font-normal">
                        {message.content}
                      </p>
                    </div>

                    <div className="flex items-center justify-between mt-4">
                      <div className="text-xs text-gray-400 font-medium bg-white/5 px-3 py-1 rounded-full backdrop-blur-sm">
                        {new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                      </div>
                      {message.role === "assistant" && ttsSupported && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-9 w-9 p-0 text-gray-400 hover:text-white hover:bg-white/10 rounded-xl backdrop-blur-sm border border-white/10 hover:border-white/20 transition-all duration-300 hover:scale-105"
                          onClick={() => handleSpeak(message.content)}
                        >
                          {isSpeaking ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}

          {isLoading && (
            <div className="py-12 border-b border-white/10 bg-gradient-to-r from-white/5 via-white/10 to-white/5 backdrop-blur-xl animate-in slide-in-from-bottom-4 duration-700">
              <div className="max-w-6xl mx-auto px-8 lg:px-12">
                <div className="flex gap-8 items-start">
                  <div className="flex-shrink-0">
                    <div className="w-16 h-16 bg-gradient-to-br from-white via-gray-100 to-gray-200 rounded-3xl flex items-center justify-center shadow-2xl shadow-white/20 backdrop-blur-xl border border-white/30 animate-pulse">
                      <Bot className="h-8 w-8 text-black" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <div className="flex space-x-4 p-8 rounded-3xl bg-gradient-to-br from-white/10 to-white/20 border border-white/30 backdrop-blur-xl">
                      <div className="w-4 h-4 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-bounce shadow-lg"></div>
                      <div
                        className="w-4 h-4 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-bounce shadow-lg"
                        style={{ animationDelay: "0.1s" }}
                      ></div>
                      <div
                        className="w-4 h-4 bg-gradient-to-r from-pink-400 to-red-400 rounded-full animate-bounce shadow-lg"
                        style={{ animationDelay: "0.2s" }}
                      ></div>
                    </div>
                    <p className="text-gray-400 text-lg mt-4 animate-pulse font-medium">Thinking...</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>

      <div className="border-t border-white/20 bg-gradient-to-t from-black via-black/95 to-black/90 backdrop-blur-2xl relative">
        <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent pointer-events-none"></div>
        <div className="max-w-6xl mx-auto p-8 sm:p-12 lg:p-16 relative z-10">
          {/* Uploaded Files Preview */}
          {uploadedFiles.length > 0 && (
            <div className="mb-4 sm:mb-6">
              <div className="flex flex-wrap gap-2 sm:gap-3 space-responsive">
                {uploadedFiles.map((file) => (
                  <div
                    key={file.id}
                    className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-2 sm:p-3 flex items-center gap-2 sm:gap-3 text-xs sm:text-sm hover:bg-white/15 transition-all duration-300"
                  >
                    {file.type.startsWith("image/") ? (
                      <ImageIcon className="h-4 w-4 sm:h-5 sm:w-5 text-blue-400" />
                    ) : (
                      <FileText className="h-4 w-4 sm:h-5 sm:w-5 text-green-400" />
                    )}
                    <span className="truncate max-w-[100px] sm:max-w-[150px] text-white">{file.name}</span>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-5 w-5 sm:h-6 sm:w-6 p-0 text-gray-400 hover:text-white touch-target hover:bg-white/10 rounded-lg" 
                      onClick={() => removeFile(file.id)}
                    >
                      <X className="h-3 w-3 sm:h-4 sm:w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Enhanced Model Selector */}
          <div className="mb-8">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="h-14 px-6 text-white hover:text-white bg-gradient-to-r from-white/10 to-white/20 hover:from-white/20 hover:to-white/30 rounded-2xl backdrop-blur-xl border border-white/30 hover:border-white/40 transition-all duration-500 hover:scale-105 shadow-lg hover:shadow-xl"
                >
                  <Bot className="h-5 w-5 mr-3" />
                  <span className="font-semibold">{models.find(m => m.id === selectedModel)?.name || "Select Model"}</span>
                  <ChevronDown className="h-5 w-5 ml-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-black/95 backdrop-blur-2xl border border-white/30 text-white rounded-2xl p-2 shadow-2xl">
                {models.map((model) => (
                  <DropdownMenuItem
                    key={model.id}
                    onClick={() => setSelectedModel(model.id)}
                    className="hover:bg-white/10 focus:bg-white/10 rounded-xl p-4 transition-all duration-300"
                  >
                    <div className="flex flex-col">
                      <span className="font-semibold text-lg">{model.name}</span>
                      <span className="text-sm text-gray-400 mt-1">{model.description}</span>
                    </div>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <form onSubmit={onSubmit}>
            <div className="relative flex items-end gap-6">
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-16 w-16 text-gray-400 hover:text-white bg-gradient-to-br from-white/10 to-white/20 hover:from-white/20 hover:to-white/30 rounded-3xl backdrop-blur-xl border border-white/30 hover:border-white/40 transition-all duration-500 hover:scale-110 hover:rotate-3 shadow-lg hover:shadow-xl"
                onClick={() => setShowDocumentUpload(true)}
              >
                <Paperclip className="h-7 w-7" />
              </Button>

              <div className="flex-1 relative">
                <Input
                  value={input}
                  onChange={handleInputChange}
                  placeholder={uploadedFiles.length > 0 ? "Ask about your documents..." : "Message ChatNova..."}
                  disabled={isLoading}
                  className="min-h-[72px] pr-32 py-6 px-8 text-lg bg-gradient-to-r from-white/10 to-white/20 backdrop-blur-xl border border-white/30 rounded-3xl focus:border-white/50 focus:ring-0 focus:bg-gradient-to-r focus:from-white/20 focus:to-white/30 resize-none text-white placeholder:text-gray-300 transition-all duration-500 hover:from-white/15 hover:to-white/25 shadow-lg focus:shadow-xl"
                />
                <div className="absolute right-4 top-1/2 -translate-y-1/2 flex items-center gap-3">
                  <VoiceInput
                    onTranscript={(text) => handleInputChange({ target: { value: text } } as any)}
                    onCommand={handleVoiceCommand}
                    className="h-12 w-12 text-gray-400 hover:text-white bg-gradient-to-br from-white/10 to-white/20 hover:from-white/20 hover:to-white/30 rounded-2xl backdrop-blur-xl border border-white/30 hover:border-white/40 transition-all duration-500 hover:scale-110"
                  />
                  <Button
                    type="submit"
                    disabled={isLoading || (!input.trim() && uploadedFiles.length === 0)}
                    size="sm"
                    className="h-12 w-12 p-0 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 text-white hover:from-blue-600 hover:via-purple-600 hover:to-pink-600 disabled:opacity-50 disabled:bg-gray-600 rounded-2xl transition-all duration-500 hover:scale-110 hover:rotate-3 shadow-xl shadow-blue-500/30 hover:shadow-2xl hover:shadow-purple-500/40"
                  >
                    <Send className="h-6 w-6" />
                  </Button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>

      {/* Document Upload Modal */}
      <DocumentUpload
        open={showDocumentUpload}
        onOpenChange={setShowDocumentUpload}
        onFilesUploaded={handleFileUpload}
      />
    </div>
  )
}