"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  Send,
  Bot,
  User,
  Sparkles,
  Volume2,
  VolumeX,
  Paperclip,
  FileText,
  ImageIcon,
  X,
  Download,
  Mic,
  MicOff,
  ChevronDown,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useLanguage } from "@/components/language-provider"
import { useChatHistory } from "@/hooks/use-chat-history"
import { useTextToSpeech } from "@/hooks/use-text-to-speech"
import { DocumentUpload } from "@/components/document-upload"
import { VoiceInput } from "@/components/voice-input"
import { cn } from "@/lib/utils"

interface ChatInterfaceProps {
  conversationId: string | null
}

interface UploadedFile {
  id: string
  name: string
  type: string
  size: number
  content: string
  url?: string
}

export function ChatInterface({ conversationId }: ChatInterfaceProps) {
  const { t } = useLanguage()
  const { saveMessage, getConversationMessages } = useChatHistory()
  const [initialMessages, setInitialMessages] = useState<any[]>([])
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([])
  const [showDocumentUpload, setShowDocumentUpload] = useState(false)
  const [selectedModel, setSelectedModel] = useState<string>("gemini")

  const models = [
    { id: "gemini", name: "Gemini Pro", description: "Google's advanced AI model" },
    { id: "gemma-3", name: "Gemma 3", description: "Google's open-source model via OpenRouter" },
    { id: "deepseek", name: "DeepSeek Chat", description: "DeepSeek's reasoning model via OpenRouter" },
  ]

  const [messages, setMessages] = useState<any[]>([])
  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  // Custom chat handler since we're using JSON responses
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim() && uploadedFiles.length === 0) return

    const userMessage = {
      id: Date.now().toString(),
      role: "user",
      content: input,
      files: uploadedFiles,
    }

    setMessages(prev => [...prev, userMessage])
    setInput("")
    setIsLoading(true)

    try {
      const response = await fetch("/api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          messages: [...messages, userMessage],
          uploadedFiles: uploadedFiles,
          selectedModel: selectedModel,
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to get response")
      }

      const data = await response.json()

      if (data.error) {
        throw new Error(data.error)
      }

      const assistantMessage = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: data.content,
        model: data.model,
      }

      setMessages(prev => [...prev, assistantMessage])

      if (conversationId) {
        saveMessage(conversationId, userMessage)
        saveMessage(conversationId, assistantMessage)
      }
    } catch (error) {
      console.error("Chat error:", error)
      const errorMessage = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: `Sorry, I encountered an error: ${error instanceof Error ? error.message : "Unknown error"}`,
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
      setUploadedFiles([])
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value)
  }

  // Handle voice commands
  const handleVoiceCommand = (command: string, args?: string) => {
    switch (command) {
      case 'upload':
        setShowDocumentUpload(true)
        break
      case 'clear':
        // Clear the input field
        setInput('')
        break
      case 'send':
        // Submit the form if there's input
        if (input.trim() || uploadedFiles.length > 0) {
          const event = new Event('submit', { bubbles: true, cancelable: true })
          document.querySelector('form')?.dispatchEvent(event)
        }
        break
      default:
        break
    }
  }
  const { speak, stop, isSpeaking, isSupported: ttsSupported } = useTextToSpeech()

  const scrollAreaRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (conversationId) {
      const savedMessages = getConversationMessages(conversationId)
      setInitialMessages(savedMessages)
    }
  }, [conversationId, getConversationMessages])

  
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight
    }
  }, [messages])

  const onSubmit = handleSubmit

  const handleFileUpload = (files: UploadedFile[]) => {
    setUploadedFiles((prev) => [...prev, ...files])
    setShowDocumentUpload(false)
  }

  const removeFile = (fileId: string) => {
    setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId))
  }

  const handleSpeak = (text: string) => {
    if (isSpeaking) {
      stop()
    } else {
      speak(text)
    }
  }

  return (
    <div className="flex-1 flex flex-col relative bg-black text-white h-full">
      <ScrollArea className="flex-1 relative h-full" ref={scrollAreaRef}>
        <div className="max-w-4xl mx-auto px-4 sm:px-6">
          {messages.length === 0 && (
            <div className="flex flex-col items-center justify-center min-h-[70vh] text-center py-12">
              <div className="mb-8">
                <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mb-6 mx-auto">
                  <Bot className="h-8 w-8 text-black" />
                </div>
                <h1 className="text-3xl sm:text-4xl font-bold text-white mb-4">
                  How can I help you today?
                </h1>
                <p className="text-gray-400 text-lg max-w-2xl mx-auto">
                  I'm your AI assistant, ready to help with anything you need.
                </p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 w-full max-w-3xl">
                <div className="p-4 bg-gray-800 border border-gray-700 rounded-lg hover:bg-gray-700 cursor-pointer transition-colors">
                  <div className="text-2xl mb-2">✍️</div>
                  <div className="text-base font-medium text-white mb-2">
                    Help me write
                  </div>
                  <div className="text-sm text-gray-400">
                    Create content, emails, or documents
                  </div>
                </div>

                <div className="group p-6 sm:p-8 bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl hover:bg-white/10 hover:border-white/20 cursor-pointer transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl hover:shadow-green-500/10 touch-target mobile-optimized relative overflow-hidden card-enhanced">
                  <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="relative z-10">
                    <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">📄</div>
                    <div className="text-lg sm:text-xl font-semibold text-white mb-3 group-hover:text-green-300 text-responsive transition-colors duration-300">
                      Analyze documents
                    </div>
                    <div className="text-sm sm:text-base text-gray-400 text-responsive leading-relaxed">
                      Upload and discuss PDF files with intelligent analysis
                    </div>
                  </div>
                </div>

                <div className="group p-6 sm:p-8 bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl hover:bg-white/10 hover:border-white/20 cursor-pointer transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl hover:shadow-purple-500/10 touch-target mobile-optimized relative overflow-hidden card-enhanced">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="relative z-10">
                    <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">💡</div>
                    <div className="text-lg sm:text-xl font-semibold text-white mb-3 group-hover:text-purple-300 text-responsive transition-colors duration-300">
                      Get answers
                    </div>
                    <div className="text-sm sm:text-base text-gray-400 text-responsive leading-relaxed">
                      Ask questions on any topic and get detailed responses
                    </div>
                  </div>
                </div>

                <div className="group p-6 sm:p-8 bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl hover:bg-white/10 hover:border-white/20 cursor-pointer transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl hover:shadow-orange-500/10 touch-target mobile-optimized relative overflow-hidden card-enhanced">
                  <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="relative z-10">
                    <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">🎤</div>
                    <div className="text-lg sm:text-xl font-semibold text-white mb-3 group-hover:text-orange-300 text-responsive transition-colors duration-300">
                      Voice chat
                    </div>
                    <div className="text-sm sm:text-base text-gray-400 text-responsive leading-relaxed">
                      Talk naturally with voice input and audio responses
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {messages.map((message, index) => (
            <div
              key={message.id}
              className={cn(
                "py-8 border-b border-white/5 last:border-b-0 message-animate",
                message.role === "assistant" ? "bg-white/5 backdrop-blur-sm" : ""
              )}
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="max-w-5xl mx-auto px-6">
                <div className="flex gap-6">
                  <div className="flex-shrink-0">
                    {message.role === "assistant" ? (
                      <div className="w-12 h-12 bg-gradient-to-br from-white via-gray-100 to-gray-200 rounded-2xl flex items-center justify-center shadow-lg shadow-white/10 backdrop-blur-md border border-white/20 hover:scale-105 transition-all duration-300">
                        <Bot className="h-6 w-6 text-black" />
                      </div>
                    ) : (
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/20 hover:scale-105 transition-all duration-300">
                        <User className="h-6 w-6 text-white" />
                      </div>
                    )}
                  </div>

                  <div className="flex-1 space-y-4">
                    {/* Enhanced Files Display */}
                    {message.files && message.files.length > 0 && (
                      <div className="flex flex-wrap gap-3 mb-4">
                        {message.files.map((file: UploadedFile) => (
                          <div
                            key={file.id}
                            className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-3 flex items-center gap-3 text-sm hover:bg-white/15 transition-all duration-300 hover:scale-105"
                          >
                            {file.type.startsWith("image/") ? (
                              <ImageIcon className="h-5 w-5 text-blue-400" />
                            ) : (
                              <FileText className="h-5 w-5 text-green-400" />
                            )}
                            <span className="truncate max-w-[150px] text-white font-medium">{file.name}</span>
                            {file.url && (
                              <Button variant="ghost" size="sm" className="h-6 w-6 p-0 text-gray-400 hover:text-white hover:bg-white/20 rounded-lg" asChild>
                                <a href={file.url} download={file.name}>
                                  <Download className="h-4 w-4" />
                                </a>
                              </Button>
                            )}
                          </div>
                        ))}
                      </div>
                    )}

                    <div className={cn(
                      "prose prose-lg max-w-none prose-invert p-4 sm:p-6 rounded-2xl message-bubble",
                      message.role === "assistant" ? "message-bubble-assistant" : "message-bubble-user"
                    )}>
                      <p className="text-white leading-relaxed whitespace-pre-wrap m-0 text-base font-normal">
                        {message.content}
                      </p>
                    </div>

                    <div className="flex items-center justify-between mt-4">
                      <div className="text-xs text-gray-400 font-medium bg-white/5 px-3 py-1 rounded-full backdrop-blur-sm">
                        {new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                      </div>
                      {message.role === "assistant" && ttsSupported && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-9 w-9 p-0 text-gray-400 hover:text-white hover:bg-white/10 rounded-xl backdrop-blur-sm border border-white/10 hover:border-white/20 transition-all duration-300 hover:scale-105"
                          onClick={() => handleSpeak(message.content)}
                        >
                          {isSpeaking ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}

          {isLoading && (
            <div className="py-8 border-b border-white/5 bg-white/5 backdrop-blur-sm">
              <div className="max-w-5xl mx-auto px-6">
                <div className="flex gap-6">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-gradient-to-br from-white via-gray-100 to-gray-200 rounded-2xl flex items-center justify-center shadow-lg shadow-white/10 backdrop-blur-md border border-white/20 animate-pulse">
                      <Bot className="h-6 w-6 text-black" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <div className="flex space-x-3 p-4 sm:p-6 rounded-2xl message-bubble message-bubble-assistant">
                      <div className="w-3 h-3 bg-white/50 rounded-full animate-bounce"></div>
                      <div
                        className="w-3 h-3 bg-white/50 rounded-full animate-bounce"
                        style={{ animationDelay: "0.1s" }}
                      ></div>
                      <div
                        className="w-3 h-3 bg-white/50 rounded-full animate-bounce"
                        style={{ animationDelay: "0.2s" }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>

      <div className="border-t border-white/10 bg-black/40 backdrop-blur-xl relative">
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent pointer-events-none"></div>
        <div className="max-w-5xl mx-auto p-6 sm:p-8 padding-responsive relative z-10">
          {/* Uploaded Files Preview */}
          {uploadedFiles.length > 0 && (
            <div className="mb-4 sm:mb-6">
              <div className="flex flex-wrap gap-2 sm:gap-3 space-responsive">
                {uploadedFiles.map((file) => (
                  <div
                    key={file.id}
                    className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-2 sm:p-3 flex items-center gap-2 sm:gap-3 text-xs sm:text-sm hover:bg-white/15 transition-all duration-300"
                  >
                    {file.type.startsWith("image/") ? (
                      <ImageIcon className="h-4 w-4 sm:h-5 sm:w-5 text-blue-400" />
                    ) : (
                      <FileText className="h-4 w-4 sm:h-5 sm:w-5 text-green-400" />
                    )}
                    <span className="truncate max-w-[100px] sm:max-w-[150px] text-white">{file.name}</span>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-5 w-5 sm:h-6 sm:w-6 p-0 text-gray-400 hover:text-white touch-target hover:bg-white/10 rounded-lg" 
                      onClick={() => removeFile(file.id)}
                    >
                      <X className="h-3 w-3 sm:h-4 sm:w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Model Selector */}
          <div className="mb-4">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="h-10 px-4 text-gray-400 hover:text-white hover:bg-white/10 rounded-xl backdrop-blur-md border border-white/10 hover:border-white/20 transition-all duration-300"
                >
                  <Bot className="h-4 w-4 mr-2" />
                  {models.find(m => m.id === selectedModel)?.name || "Select Model"}
                  <ChevronDown className="h-4 w-4 ml-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-black/90 backdrop-blur-md border border-white/20 text-white">
                {models.map((model) => (
                  <DropdownMenuItem
                    key={model.id}
                    onClick={() => setSelectedModel(model.id)}
                    className="hover:bg-white/10 focus:bg-white/10"
                  >
                    <div className="flex flex-col">
                      <span className="font-medium">{model.name}</span>
                      <span className="text-xs text-gray-400">{model.description}</span>
                    </div>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <form onSubmit={onSubmit}>
            <div className="relative flex items-end gap-2 sm:gap-4 space-responsive">
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-10 w-10 sm:h-12 sm:w-12 text-gray-400 hover:text-white hover:bg-white/10 rounded-xl touch-target mobile-optimized backdrop-blur-md border border-white/10 hover:border-white/20 transition-all duration-300"
                onClick={() => setShowDocumentUpload(true)}
              >
                <Paperclip className="h-5 w-5 sm:h-6 sm:w-6" />
              </Button>

              <div className="flex-1 relative">
                <Input
                  value={input}
                  onChange={handleInputChange}
                  placeholder={uploadedFiles.length > 0 ? "Ask about your documents..." : "Message ChatNova..."}
                  disabled={isLoading}
                  className="min-h-[56px] sm:min-h-[64px] pr-20 sm:pr-24 py-4 sm:py-5 text-sm sm:text-base bg-white/10 backdrop-blur-md border border-white/20 rounded-3xl focus:border-white/40 focus:ring-0 focus:bg-white/15 resize-none text-white placeholder:text-gray-300 transition-all duration-300 hover:bg-white/15 mobile-optimized input-enhanced"
                />
                <div className="absolute right-2 sm:right-3 top-1/2 -translate-y-1/2 flex items-center gap-1 sm:gap-2">
                  <VoiceInput
                    onTranscript={(text) => handleInputChange({ target: { value: text } } as any)}
                    onCommand={handleVoiceCommand}
                    className="h-9 w-9 sm:h-11 sm:w-11 text-gray-400 hover:text-white hover:bg-white/10 rounded-xl touch-target mobile-optimized backdrop-blur-md border border-white/10 hover:border-white/20 transition-all duration-300"
                  />
                  <Button
                    type="submit"
                    disabled={isLoading || (!input.trim() && uploadedFiles.length === 0)}
                    size="sm"
                    className="h-9 w-9 sm:h-11 sm:w-11 p-0 bg-gradient-to-br from-blue-500 to-purple-600 text-white hover:opacity-90 disabled:opacity-50 disabled:bg-gray-600 rounded-xl touch-target mobile-optimized transition-all duration-300 hover:scale-105 shadow-lg shadow-blue-500/20"
                  >
                    <Send className="h-4 w-4 sm:h-5 sm:w-5" />
                  </Button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>

      {/* Document Upload Modal */}
      <DocumentUpload
        open={showDocumentUpload}
        onOpenChange={setShowDocumentUpload}
        onFilesUploaded={handleFileUpload}
      />
    </div>
  )
}