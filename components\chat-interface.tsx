"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { ChatMessage } from "@/components/chat-message"
import { UploadedFiles } from "@/components/uploaded-files"
import { 
  Send, 
  Paperclip, 
  Mic, 
  MicOff, 
  FileText, 
  Loader2, 
  ChevronDown,
  Sparkles,
  Zap
} from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

interface Message {
  id: string
  role: "user" | "assistant" | "system"
  content: string
  createdAt: Date
}

interface UploadedFile {
  id: string
  name: string
  type: string
  size: number
  content: string
  url?: string
}

interface ChatInterfaceProps {
  messages: Message[]
  input: string
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleSubmit: (e: React.FormEvent<HTMLFormElement>) => void
  isLoading: boolean
  error: Error | null
  uploadedFiles: UploadedFile[]
  onRemoveFile: (id: string) => void
  onShowFileUpload: () => void
  selectedModel: "gemini" | "grok"
  messagesEndRef: React.RefObject<HTMLDivElement>
}

export function ChatInterface({
  messages,
  input,
  handleInputChange,
  handleSubmit,
  isLoading,
  error,
  uploadedFiles,
  onRemoveFile,
  onShowFileUpload,
  selectedModel,
  messagesEndRef
}: ChatInterfaceProps) {
  const { toast } = useToast()
  const [isListening, setIsListening] = useState(false)
  const [autoScroll, setAutoScroll] = useState(true)
  const [showScrollButton, setShowScrollButton] = useState(false)
  const messagesContainerRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Handle voice input
  const toggleVoiceInput = () => {
    if (!isListening) {
      // Start voice recognition
      if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
        const recognition = new SpeechRecognition()
        
        recognition.continuous = true
        recognition.interimResults = true
        recognition.lang = 'en-US'
        
        recognition.onresult = (event) => {
          const transcript = Array.from(event.results)
            .map(result => result[0])
            .map(result => result.transcript)
            .join('')
          
          if (inputRef.current) {
            inputRef.current.value = transcript
            // Trigger onChange to update state
            const changeEvent = new Event('input', { bubbles: true })
            inputRef.current.dispatchEvent(changeEvent)
          }
        }
        
        recognition.onend = () => {
          setIsListening(false)
        }
        
        recognition.start()
        setIsListening(true)
        
        // Store recognition instance to stop it later
        (window as any).recognition = recognition
      } else {
        toast({
          title: "Voice input not supported",
          description: "Your browser doesn't support voice recognition",
          variant: "destructive",
        })
      }
    } else {
      // Stop voice recognition
      if ((window as any).recognition) {
        (window as any).recognition.stop()
      }
      setIsListening(false)
    }
  }

  // Handle scroll events to detect when user has scrolled up
  useEffect(() => {
    const container = messagesContainerRef.current
    if (!container) return

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container
      const isScrolledUp = scrollHeight - scrollTop - clientHeight > 100
      
      setAutoScroll(!isScrolledUp)
      setShowScrollButton(isScrolledUp)
    }

    container.addEventListener("scroll", handleScroll, { passive: true })
    return () => container.removeEventListener("scroll", handleScroll)
  }, [])

  // Scroll to bottom when messages change if autoScroll is true
  useEffect(() => {
    if (autoScroll && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" })
    }
  }, [messages, autoScroll, messagesEndRef])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
    setAutoScroll(true)
  }

  // Welcome message when no messages
  const WelcomeScreen = () => (
    <div className="flex flex-col items-center justify-center h-full max-w-3xl mx-auto px-4 text-center">
      <div className="w-16 h-16 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg mb-6">
        {selectedModel === "gemini" ? (
          <Sparkles className="h-8 w-8 text-white" />
        ) : (
          <Zap className="h-8 w-8 text-white" />
        )}
      </div>
      
      <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
        How can I help you today?
      </h2>
      
      <p className="text-gray-600 dark:text-gray-400 mb-8 max-w-md">
        {selectedModel === "gemini" 
          ? "ChatNova with Google Gemini can help with questions, creative tasks, and document analysis."
          : "ChatNova with Grok can help with questions, creative tasks, and document analysis."}
      </p>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 w-full max-w-lg">
        {[
          "Explain quantum computing",
          "Write a poem about technology",
          "Summarize this document",
          "Help me debug my code",
          "Create a weekly meal plan",
          "Translate this text to Spanish"
        ].map((suggestion, index) => (
          <Button
            key={index}
            variant="outline"
            className="justify-start text-left h-auto py-3 px-4 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
            onClick={() => {
              if (inputRef.current) {
                inputRef.current.value = suggestion
                // Trigger onChange to update state
                const changeEvent = new Event('input', { bubbles: true })
                inputRef.current.dispatchEvent(changeEvent)
                inputRef.current.focus()
              }
            }}
          >
            <div className="flex items-start gap-3">
              <div className="bg-blue-100 dark:bg-blue-900/30 p-1.5 rounded-lg">
                <Sparkles className="h-3.5 w-3.5 text-blue-600 dark:text-blue-400" />
              </div>
              <span className="text-sm">{suggestion}</span>
            </div>
          </Button>
        ))}
      </div>
    </div>
  )

  return (
    <div className="flex flex-col flex-1 h-full overflow-hidden">
      {/* Uploaded Files Display */}
      {uploadedFiles.length > 0 && (
        <div className="p-4 border-b border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-900">
          <div className="flex items-center gap-2 mb-3">
            <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {uploadedFiles.length} document{uploadedFiles.length > 1 ? 's' : ''} attached
            </span>
          </div>
          <UploadedFiles files={uploadedFiles} onRemoveFile={onRemoveFile} />
        </div>
      )}

      {/* Messages Container */}
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto"
      >
        {messages.length === 0 ? (
          <WelcomeScreen />
        ) : (
          <div className="flex flex-col py-4 max-w-3xl mx-auto">
            {messages.map((message) => (
              <ChatMessage key={message.id} message={message} />
            ))}
            
            {isLoading && (
              <ChatMessage
                message={{
                  id: "loading",
                  role: "assistant",
                  content: "",
                  createdAt: new Date(),
                }}
                isLoading={true}
              />
            )}
            
            {error && (
              <div className="mx-4 my-2 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg text-red-600 dark:text-red-400 text-sm">
                <p className="font-medium">Error: {error.message}</p>
                <p className="mt-1 text-xs">Please try again or refresh the page.</p>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        )}
        
        {/* Scroll to bottom button */}
        {showScrollButton && (
          <button 
            onClick={scrollToBottom}
            className="absolute bottom-24 right-4 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 rounded-full p-2 shadow-md"
            aria-label="Scroll to bottom"
          >
            <ChevronDown className="w-5 h-5 text-gray-700 dark:text-gray-300" />
          </button>
        )}
      </div>

      {/* Input Section */}
      <div className="border-t border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 p-4">
        <div className="max-w-3xl mx-auto">
          <form onSubmit={handleSubmit} className="flex items-end gap-2">
            <div className="relative flex-1">
              <input
                ref={inputRef}
                type="text"
                value={input}
                onChange={handleInputChange}
                placeholder={`Message ${selectedModel === "gemini" ? "Gemini" : "Grok"}...`}
                className="w-full rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 py-3 pr-20 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
                disabled={isLoading}
              />
              
              <div className="absolute right-2 bottom-2 flex items-center gap-1">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={toggleVoiceInput}
                        className={`h-8 w-8 rounded-md ${
                          isListening
                            ? 'text-red-500 dark:text-red-400 bg-red-50 dark:bg-red-900/20'
                            : 'text-gray-500 dark:text-gray-400'
                        }`}
                      >
                        {isListening ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{isListening ? 'Stop voice input' : 'Start voice input'}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={onShowFileUpload}
                        className="h-8 w-8 rounded-md text-gray-500 dark:text-gray-400"
                      >
                        <Paperclip className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Upload documents</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
            
            <Button
              type="submit"
              disabled={isLoading || !input.trim()}
              className="rounded-lg bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 h-12 w-12 flex items-center justify-center"
            >
              {isLoading ? (
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : (
                <Send className="h-5 w-5" />
              )}
            </Button>
          </form>
          
          <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 text-center">
            {selectedModel === "gemini" ? (
              <div className="flex items-center justify-center gap-1">
                <Sparkles className="h-3 w-3" />
                <span>Powered by Google Gemini</span>
              </div>
            ) : (
              <div className="flex items-center justify-center gap-1">
                <Zap className="h-3 w-3" />
                <span>Powered by Grok</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}